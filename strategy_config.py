"""
统一策略配置文件
确保回测和实盘系统使用完全相同的参数
"""

class StrategyConfig:
    """统一策略配置类 - 回测和实盘共用"""
    
    # =============================================================================
    # 数据参数
    # =============================================================================
    bar_interval = 5  # K线周期(分钟)
    lookback_window = 60  # 特征计算的回溯窗口
    predict_window = 3    # 预测窗口
    min_bar_count = 80  # 开始交易所需的最小K线数量
    
    # =============================================================================
    # 核心策略参数 - 与回测EnhancedFuturesMlStrategy完全一致
    # =============================================================================
    open_threshold = 0.002        # 开仓阈值
    close_threshold = 0.001       # 平仓阈值
    stop_loss = 0.02              # 止损比例 (2%)
    profit_target = 0.04          # 止盈比例 (4%)
    max_holding_days = 15         # 最大持仓天数
    max_positions = 1             # 最大持仓数量
    
    # 订单执行参数
    price_add = 0.0005            # 价格调整比例
    
    # =============================================================================
    # 信号处理参数
    # =============================================================================
    signal_ma_length = 3          # 信号移动平均长度
    
    # =============================================================================
    # 趋势过滤参数
    # =============================================================================
    trend_filter_window = 10      # 趋势过滤窗口
    trend_threshold = 0.6         # 趋势强度阈值
    
    # =============================================================================
    # 波动率过滤参数
    # =============================================================================
    volatility_filter_window = 5  # 波动率过滤窗口
    volatility_threshold = 0.02   # 波动率阈值
    
    # =============================================================================
    # 动态止损止盈参数
    # =============================================================================
    dynamic_stop_atr_multiple = 2.0  # ATR倍数用于动态止损
    trailing_stop_activation = 0.01  # 触发跟踪止损的盈利比例 (1%)
    trailing_stop_distance = 0.005   # 跟踪止损距离 (0.5%)
    
    # =============================================================================
    # 特征工程参数
    # =============================================================================
    # 价格特征窗口
    price_windows = [1, 2, 3, 5, 10, 20, 30]
    
    # 动量指标窗口
    momentum_windows = [5, 10, 20]
    
    # 波动率指标窗口
    volatility_windows = [5, 10, 20]
    
    # 成交量特征窗口
    volume_windows = [5, 10, 20]
    
    # 趋势强度指标窗口
    trend_windows = [5, 10, 20, 30, 60]
    
    # =============================================================================
    # 数据处理参数
    # =============================================================================
    # 缺失值处理
    missing_value_threshold = 0.25  # 缺失值比例阈值
    
    # 标准化参数
    normalization_window = 20  # 滚动标准化窗口
    
    # 异常值处理
    winsorize_lower = 0.01  # 下分位数
    winsorize_upper = 0.99  # 上分位数
    
    # =============================================================================
    # 交易执行参数
    # =============================================================================
    lots = 1  # 交易手数
    price_tick = 0.02  # 最小价格变动(黄金期货)
    price_offset = 5  # 下单价格偏移量(单位：跳)
    
    # =============================================================================
    # 风险管理参数
    # =============================================================================
    max_position_ratio = 0.2  # 最大仓位比例（20%）
    max_daily_loss_ratio = 0.05  # 日最大亏损比例（5%）
    min_available_margin_ratio = 0.3  # 最小可用保证金比例（30%）
    max_orders_per_day = 50  # 每日最大下单次数
    max_position_per_symbol = 5  # 单品种最大持仓手数
    
    # =============================================================================
    # 模型参数
    # =============================================================================
    # LightGBM参数
    lgb_params = {
        'objective': 'regression',
        'metric': 'rmse',
        'boosting_type': 'gbdt',
        'num_leaves': 31,
        'learning_rate': 0.05,
        'feature_fraction': 0.9,
        'bagging_fraction': 0.8,
        'bagging_freq': 5,
        'verbose': -1,
        'random_state': 42
    }
    
    # 特征选择参数
    feature_importance_threshold = 0.01  # 特征重要性阈值
    max_features = None  # 最大特征数量（None表示不限制）
    
    # =============================================================================
    # 在线学习参数
    # =============================================================================
    retrain_threshold = 100  # 新数据达到100条时重训练
    validation_window = 5  # 交叉验证窗口
    min_improvement = 0.05  # 模型改善的最小阈值(5%)
    max_model_age_days = 7  # 模型最大存在天数，超过后强制重训练
    online_learning_enabled = True  # 是否启用在线学习
    
    # =============================================================================
    # 路径配置
    # =============================================================================
    model_dir = "./models"  # 模型保存目录
    feature_dir = "./features"  # 特征列表保存目录
    data_dir = "./data"  # 数据保存目录
    log_dir = "./logs"  # 日志目录
    
    # =============================================================================
    # 系统参数
    # =============================================================================
    max_retry_times = 3  # 最大重试次数
    retry_delay = 5  # 重试延迟(秒)
    heartbeat_interval = 30  # 心跳间隔(秒)
    
    # =============================================================================
    # 回测专用参数
    # =============================================================================
    # 回测时间段
    train_start = "2023-01-01"
    train_end = "2023-10-31"
    valid_start = "2023-11-01"
    valid_end = "2023-11-30"
    test_start = "2023-12-01"
    test_end = "2024-01-31"
    
    # 优化参数范围
    optimization_params = {
        'open_threshold': [0.001, 0.002, 0.003, 0.004, 0.005],
        'close_threshold': [0.0005, 0.001, 0.0015, 0.002],
        'stop_loss': [0.015, 0.02, 0.025, 0.03],
        'profit_target': [0.03, 0.04, 0.05, 0.06],
        'max_holding_days': [10, 15, 20, 25],
        'signal_ma_length': [2, 3, 4, 5],
        'trend_threshold': [0.55, 0.6, 0.65, 0.7],
        'volatility_threshold': [0.015, 0.02, 0.025, 0.03]
    }
    
    # =============================================================================
    # 实盘专用参数
    # =============================================================================
    # CTP连接参数（需要根据实际情况填写）
    simnow_account = {
        'user_id': '222809',
        'password': '4179482gyj~',
        'server_name': '电信1',  
        'subscribe_list': [b'au2510'],
    }
    real_account = {
        'broker_id': '9999',
        'server_dict': {"TDServer":"ip:port","MDServer":"ip:port"},
        "reserve_server_dict":{},
        "user_id":'',
        "password":'',
        "app_id":'',
        "auth_code":'',
        'subscribe_list': ['au2510'],
    }
    
    # 交易时间段
    trading_sessions = [
        ('09:00:00', '10:15:00'),  # 上午第一节
        ('10:30:00', '11:30:00'),  # 上午第二节
        ('13:30:00', '15:00:00'),  # 下午
        ('21:00:00', '02:30:00')   # 夜盘
    ]
    
    # 数据API配置
    dataapi_config = {
        'username': '***********',
        'password': '4179482gyj',
        'base_url': 'http://kanpan789.com:8086/ftdata'
    }
    
    @classmethod
    def get_backtest_config(cls):
        """获取回测配置"""
        config = {}
        for attr in dir(cls):
            if not attr.startswith('_') and not callable(getattr(cls, attr)):
                config[attr] = getattr(cls, attr)
        return config
    
    @classmethod
    def get_live_config(cls):
        """获取实盘配置"""
        config = cls.get_backtest_config()
        # 实盘可能需要调整的参数
        config['online_learning_enabled'] = True
        return config
    
    @classmethod
    def validate_config(cls):
        """验证配置参数的合理性"""
        errors = []
        
        # 检查阈值参数
        if cls.open_threshold <= cls.close_threshold:
            errors.append("开仓阈值应大于平仓阈值")
        
        if cls.stop_loss <= 0 or cls.stop_loss >= 1:
            errors.append("止损比例应在0-1之间")
        
        if cls.profit_target <= 0 or cls.profit_target >= 1:
            errors.append("止盈比例应在0-1之间")
        
        if cls.profit_target <= cls.stop_loss:
            errors.append("止盈比例应大于止损比例")
        
        # 检查窗口参数
        if cls.lookback_window < max(cls.price_windows):
            errors.append("回溯窗口应大于最大特征窗口")
        
        if cls.min_bar_count < cls.lookback_window:
            errors.append("最小K线数量应大于回溯窗口")
        
        # 检查风险参数
        if cls.max_position_ratio <= 0 or cls.max_position_ratio > 1:
            errors.append("最大仓位比例应在0-1之间")
        
        if cls.max_daily_loss_ratio <= 0 or cls.max_daily_loss_ratio > 1:
            errors.append("日最大亏损比例应在0-1之间")
        
        return errors
    
    @classmethod
    def print_config(cls):
        """打印配置信息"""
        print("=" * 60)
        print("策略配置参数")
        print("=" * 60)
        
        sections = {
            '核心策略参数': ['open_threshold', 'close_threshold', 'stop_loss', 'profit_target', 
                          'max_holding_days', 'max_positions'],
            '信号处理参数': ['signal_ma_length', 'trend_filter_window', 'trend_threshold',
                          'volatility_filter_window', 'volatility_threshold'],
            '动态止损参数': ['dynamic_stop_atr_multiple', 'trailing_stop_activation', 'trailing_stop_distance'],
            '风险管理参数': ['max_position_ratio', 'max_daily_loss_ratio', 'min_available_margin_ratio',
                          'max_orders_per_day', 'max_position_per_symbol'],
            '数据处理参数': ['lookback_window', 'predict_window', 'min_bar_count', 'bar_interval']
        }
        
        for section_name, params in sections.items():
            print(f"\n{section_name}:")
            print("-" * 40)
            for param in params:
                if hasattr(cls, param):
                    value = getattr(cls, param)
                    print(f"  {param}: {value}")
        
        print("=" * 60)

# 验证配置
if __name__ == "__main__":
    # 验证配置参数
    errors = StrategyConfig.validate_config()
    if errors:
        print("配置验证失败:")
        for error in errors:
            print(f"  - {error}")
    else:
        print("配置验证通过")
    
    # 打印配置信息
    StrategyConfig.print_config()