"""
LightGBM期货策略实盘交易系统（增强版）
与回测系统保持一致的策略逻辑

版本: 2.1
更新时间: 2025-07-15
修改: 确保与回测策略一致
"""

from datetime import datetime, timedelta, time as dt_time
import re
from collections import defaultdict, deque
import os
import numpy as np
import polars as pl
import pandas as pd
from tqdm import tqdm
import logging
import sys
import pickle
import time
import threading
from pathlib import Path
from logging.handlers import RotatingFileHandler
import warnings
import json
from sklearn.model_selection import TimeSeriesSplit, train_test_split
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
import joblib
from strategy_config import StrategyConfig
warnings.filterwarnings("ignore", category=FutureWarning)

# CTP接口相关
from AlgoPlus.CTP.MdApi import run_tick_engine
from AlgoPlus.CTP.FutureAccount import get_simulate_account, FutureAccount
from AlgoPlus.CTP.TraderApiBase import TraderApiBase
from multiprocessing import Process, Queue

# LightGBM模型
import lightgbm as lgb

import os

# 全局变量
model_dict = {}  # 存储加载的LightGBM模型
feature_names_dict = {}  # 存储模型特征名称
bar_data_dict = {}  # 存储K线数据
previous_volume = {}  # 记录上一个Tick的成交量

def setup_logging(log_folder='logs'):
    """配置增强的日志记录器"""
    import os
    # 只创建主logs目录，不创建子文件夹
    os.makedirs(log_folder, exist_ok=True)
    
    # 详细格式化器
    detailed_formatter = logging.Formatter(
        '%(asctime)s.%(msecs)03d | %(levelname)s | %(name)s | %(funcName)s:%(lineno)d | %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # 简化格式化器（用于控制台）
    simple_formatter = logging.Formatter(
        '%(asctime)s | %(levelname)s | %(name)s | %(message)s',
        datefmt='%H:%M:%S'
    )
    
    # 创建各类日志记录器配置 - 统一存放在logs文件夹
    loggers_config = {
        'system': {'file': f'{log_folder}/system.log', 'level': logging.INFO, 'console': True},
        'data_processing': {'file': f'{log_folder}/data.log', 'level': logging.INFO, 'console': False},
        'model_prediction': {'file': f'{log_folder}/model.log', 'level': logging.INFO, 'console': False},
        'trading_signals': {'file': f'{log_folder}/signal.log', 'level': logging.INFO, 'console': True},
        'risk_management': {'file': f'{log_folder}/risk.log', 'level': logging.INFO, 'console': True},
        'order_execution': {'file': f'{log_folder}/order.log', 'level': logging.INFO, 'console': True},
        'position_management': {'file': f'{log_folder}/position.log', 'level': logging.INFO, 'console': True},
        'performance': {'file': f'{log_folder}/performance.log', 'level': logging.INFO, 'console': False},
        'error_handling': {'file': f'{log_folder}/error.log', 'level': logging.WARNING, 'console': True},
        'compliance': {'file': f'{log_folder}/compliance.log', 'level': logging.INFO, 'console': False},
        'stoploss': {'file': f'{log_folder}/stoploss.log', 'level': logging.INFO, 'console': True},
        'dynamic_learning': {'file': f'{log_folder}/learning.log', 'level': logging.INFO, 'console': False}
    }
    
    loggers = {}
    for name, config in loggers_config.items():
        logger = logging.getLogger(name)
        logger.setLevel(config['level'])
        
        # 清除现有处理器
        logger.handlers.clear()
        
        # 使用支持安全轮转的文件处理器
        from logging.handlers import RotatingFileHandler
        import os
        
        try:
            # 确保日志目录存在
            os.makedirs(os.path.dirname(config['file']), exist_ok=True)
            
            # 使用RotatingFileHandler而不是TimedRotatingFileHandler
            # 避免文件重命名导致的权限问题
            file_handler = RotatingFileHandler(
                config['file'],
                maxBytes=50*1024*1024,  # 50MB
                backupCount=30,
                encoding='utf-8',
                delay=True  # 延迟文件创建
            )
            
            # 增强的错误处理，防止轮转失败
            def safe_doRollover(original_method):
                def wrapper(*args, **kwargs):
                    try:
                        return original_method(*args, **kwargs)
                    except (PermissionError, OSError) as e:
                        # 轮转失败时继续写入当前文件
                        print(f"警告: 日志文件轮转失败 {config['file']}: {e}")
                        return
                return wrapper
            
            # 为文件处理器添加安全轮转包装
            file_handler.doRollover = safe_doRollover(file_handler.doRollover)
            
        except Exception as e:
            # 如果创建文件处理器失败，使用StreamHandler作为备选
            import sys
            file_handler = logging.StreamHandler(sys.stdout)
            print(f"警告: 无法创建日志文件处理器 {config['file']}: {e}, 使用控制台输出")
        file_handler.setFormatter(detailed_formatter)
        # 强制实时写入，不使用缓冲
        file_handler.setLevel(config['level'])
        logger.addHandler(file_handler)
        
        # 控制台处理器（选择性添加）
        if config.get('console', False):
            console_handler = logging.StreamHandler()
            console_handler.setLevel(config['level'])
            console_handler.setFormatter(simple_formatter)
            logger.addHandler(console_handler)
        
        # 设置日志强制刷新
        logger.setLevel(config['level'])
        for handler in logger.handlers:
            handler.flush()
            # 禁用缓冲，立即写入
            if hasattr(handler, 'stream') and hasattr(handler.stream, 'flush'):
                handler.stream.flush()
        
        loggers[name] = logger
    
    return (loggers['trading_signals'], loggers['stoploss'], loggers['data_processing'], 
            loggers['dynamic_learning'], loggers['system'], loggers['model_prediction'],
            loggers['risk_management'], loggers['order_execution'], loggers['position_management'],
            loggers['performance'], loggers['error_handling'], loggers['compliance'])

# 全局日志记录器
(SIGNAL_LOGGER, STOPLOSS_LOGGER, DATA_LOGGER, LEARNING_LOGGER, 
 SYSTEM_LOGGER, MODEL_LOGGER, RISK_LOGGER, ORDER_LOGGER, 
 POSITION_LOGGER, PERFORMANCE_LOGGER, ERROR_LOGGER, COMPLIANCE_LOGGER) = setup_logging()


# 全局变量
tickdatadict = {}  # 存储Tick数据的字典
quotedict = {}  # 存储行情数据的字典
ofdatadict = {}  # 存储K线数据的字典
trader_df = pd.DataFrame({})  # 存储交易数据的DataFrame对象
tsymbollist = {}

def tickcome(md_queue):
    """处理tick数据 - """
    global previous_volume
    
    try:
        data = md_queue
        instrument_id = data['InstrumentID'].decode()  # 品种代码
        ActionDay = data['ActionDay'].decode()  # 交易日日期
        update_time = data['UpdateTime'].decode()  # 更新时间
        update_millisec = str(data['UpdateMillisec'])  # 更新毫秒数
        created_at = ActionDay[:4] + '-' + ActionDay[4:6] + '-' + ActionDay[6:] + ' ' + update_time + '.' + update_millisec  # 创建时间
        
        # 构建tick字典
        tick = {
            'symbol': instrument_id,  # 品种代码和交易所ID
            'created_at': datetime.strptime(created_at, "%Y-%m-%d %H:%M:%S.%f"),
            'price': float(data['LastPrice']),  # 最新价
            'last_volume': int(data['Volume']) - previous_volume.get(instrument_id, 0) if previous_volume.get(instrument_id, 0) != 0 else 0,  # 瞬时成交量
            'bid_p': float(data['BidPrice1']),  # 买价
            'bid_v': int(data['BidVolume1']),  # 买量
            'ask_p': float(data['AskPrice1']),  # 卖价
            'ask_v': int(data['AskVolume1']),  # 卖量
            'UpperLimitPrice': float(data['UpperLimitPrice']),  # 涨停价
            'LowerLimitPrice': float(data['LowerLimitPrice']),  # 跌停价
            'TradingDay': data['TradingDay'].decode(),  # 交易日日期
            'cum_volume': int(data['Volume']),  # 最新总成交量
            'cum_amount': float(data['Turnover']),  # 最新总成交额
            'cum_position': int(data['OpenInterest']),  # 合约持仓量
        }
        
        # 数据质量检查
        if tick['price'] <= 0:
            DATA_LOGGER.warning(f"Tick数据异常 | 合约: {instrument_id} | 异常类型: 价格异常 | 价格: {tick['price']}")
            return
            
        # 价格跳跃检查
        if instrument_id in previous_volume:
            price_change_ratio = abs(tick['price'] - tick.get('prev_price', tick['price'])) / tick['price']
            if price_change_ratio > 0.1:  # 10%的价格跳跃
                DATA_LOGGER.warning(f"Tick数据异常 | 合约: {instrument_id} | 异常类型: 价格跳跃 | 当前价: {tick['price']:.2f} | 跳跃幅度: {price_change_ratio:.2%}")
        
        # 更新上一个Tick的成交量
        previous_volume[instrument_id] = int(data['Volume'])
        
        if tick['last_volume'] > 0:
            DATA_LOGGER.debug(f"Tick接收 | 合约: {instrument_id} | 价格: {tick['price']:.2f} | 成交量: {tick['last_volume']} | 时间: {tick['created_at']}")
            # 处理Tick数据
            on_tick(tick)
            
    except Exception as e:
        ERROR_LOGGER.error(f"Tick数据处理异常 | 错误: {e} | 数据: {str(md_queue)[:200]}")

def on_tick(tick):
    """处理单个tick数据"""
    tm = can_time(tick['created_at'].hour, tick['created_at'].minute)
    
    if tick['last_volume'] == 0:
        return
        
    quotes = tick
    timetick = str(tick['created_at']).replace('+08:00', '')
    tsymbol = tick['symbol']
    
    if tsymbol not in tsymbollist.keys():
        tsymbollist[tsymbol] = tick
        bid_p = quotes['bid_p']
        ask_p = quotes['ask_p']
        bid_v = quotes['bid_v']
        ask_v = quotes['ask_v']
    else:
        # 获取上一个tick的买卖价和买卖量
        rquotes = tsymbollist[tsymbol]
        bid_p = rquotes['bid_p']
        ask_p = rquotes['ask_p']
        bid_v = rquotes['bid_v']
        ask_v = rquotes['ask_v']
        tsymbollist[tsymbol] = tick
        
    tick_dt = pd.DataFrame({
        'datetime': timetick,
        'symbol': tick['symbol'],
        'mainsym': tick['symbol'].rstrip('0123456789').upper(),
        'lastprice': tick['price'],
        'vol': tick['last_volume'],
        'bid_p': bid_p,
        'ask_p': ask_p,
        'bid_v': bid_v,
        'ask_v': ask_v
    }, index=[0])
    
    sym = tick_dt['symbol'][0]
    
    # 调用tickdata处理
    tickdata(tick_dt, sym)

def can_time(hour, minute):
    """时间转换"""
    hour = str(hour)
    minute = str(minute)
    if len(minute) == 1:
        minute = "0" + minute
    return int(hour + minute)

def tickdata(df, symbol):
    """tick数据处理生成K线"""
    global trader_df
    
    tickdata = pd.DataFrame({
        'datetime': df['datetime'],
        'symbol': df['symbol'],
        'lastprice': df['lastprice'],
        'volume': df['vol'],
        'bid_p': df['bid_p'],
        'bid_v': df['bid_v'],
        'ask_p': df['ask_p'],
        'ask_v': df['ask_v']
    })
    
    try:
        if symbol in tickdatadict.keys():
            rdf = tickdatadict[symbol]
            rdftm = pd.to_datetime(rdf['bartime'][0]).strftime('%Y-%m-%d %H:%M:%S')
            now = str(tickdata['datetime'][0])
            if now > rdftm:
                try:
                    oo = ofdatadict[symbol]
                    trader_df = pd.concat([trader_df, oo], ignore_index=True)
                    DATA_LOGGER.info(f"新K线生成 | 合约: {symbol} | trader_df长度: {len(trader_df)} | 最新时间: {oo.get('datetime', 'N/A').iloc[-1] if len(oo) > 0 else 'N/A'}")
                    if symbol in quotedict.keys():
                        quotedict.pop(symbol)
                    if symbol in tickdatadict.keys():
                        tickdatadict.pop(symbol)
                    if symbol in ofdatadict.keys():
                        ofdatadict.pop(symbol)
                except Exception as e:
                    DATA_LOGGER.error(f'K线生成异常 | 合约: {symbol} | 错误: {e}')
                
                tickdata['bartime'] = pd.to_datetime(tickdata['datetime'])
                tickdata['open'] = tickdata['lastprice']
                tickdata['high'] = tickdata['lastprice']
                tickdata['low'] = tickdata['lastprice']
                tickdata['close'] = tickdata['lastprice']
                tickdata['starttime'] = tickdata['datetime']
            else:
                tickdata['bartime'] = rdf['bartime']
                tickdata['open'] = rdf['open']
                tickdata['high'] = max(tickdata['lastprice'].values, rdf['high'].values)
                tickdata['low'] = min(tickdata['lastprice'].values, rdf['low'].values)
                tickdata['close'] = tickdata['lastprice']
                tickdata['volume'] = df['vol'] + rdf['volume'].values
                tickdata['starttime'] = rdf['starttime']
        else:
            print('新bar的第一个tick进入')
            tickdata['bartime'] = pd.to_datetime(tickdata['datetime'])
            tickdata['open'] = tickdata['lastprice']
            tickdata['high'] = tickdata['lastprice']
            tickdata['low'] = tickdata['lastprice']
            tickdata['close'] = tickdata['lastprice']
            tickdata['starttime'] = tickdata['datetime']
    except Exception as e:
        print('捕获到异常', e)
    
    tickdata['bartime'] = pd.to_datetime(tickdata['bartime'])
    
    # 使用5分钟K线
    resample_rule = '5T'
    
    # 使用resample_rule生成K线
    bardata = tickdata.resample(on='bartime', rule=resample_rule, label='right', closed='right').agg({
        'starttime': 'first',
        'symbol': 'last',
        'open': 'first',
        'high': 'max',
        'low': 'min',
        'close': 'last',
        'volume': 'sum'
    }).reset_index(drop=False)
    
    bardata = bardata.dropna().reset_index(drop=True)
    bardata['bartime'] = pd.to_datetime(bardata['bartime'][0]).strftime('%Y-%m-%d %H:%M:%S')
    tickdatadict[symbol] = bardata
    tickdata['volume'] = df['vol'].values
    
    # 简化的orderflow处理
    df_result = pd.DataFrame({
        'symbol': bardata['symbol'],
        'datetime': bardata['bartime'],
        'open': bardata['open'],
        'high': bardata['high'],
        'low': bardata['low'],
        'close': bardata['close'],
        'volume': bardata['volume'],
        'delta': 0,  # 简化处理
        'dj': 0  # 简化处理
    })
    
    ofdatadict[symbol] = df_result

# =============================================================================
# 数据处理函数 - 与回测系统保持一致
# =============================================================================

def advanced_process_missing_values(df: pl.DataFrame) -> pl.DataFrame:
    """综合缺失值处理策略"""
    try:
        # 更安全的列选择策略
        exclude_cols = ['datetime', 'vt_symbol', 'symbol', 'open', 'high', 'low', 'close', 'volume', 'open_interest']
        numeric_columns = [col for col in df.columns if col not in exclude_cols]
        
        if not numeric_columns:
            DATA_LOGGER.warning("没有找到需要处理的数值列")
            return df
        
        for col in numeric_columns:
            try:
                # 检查列是否存在且为数值类型
                if col not in df.columns:
                    continue
                    
                # 检查缺失比例
                missing_ratio = (df[col].null_count() + df.filter(pl.col(col).is_nan()).height) / df.height
                
                if missing_ratio > 0.5:
                    # 缺失过多，使用全局统计量
                    df = df.with_columns(pl.col(col).fill_null(pl.col(col).median()))
                elif missing_ratio > 0.25:
                    # 中等程度缺失，尝试前向填充然后后向填充
                    df = df.with_columns(pl.col(col).forward_fill().backward_fill())
                    # 对于剩余的空值，使用中位数
                    df = df.with_columns(pl.col(col).fill_null(pl.col(col).median()))
                else:
                    # 少量缺失，使用类似插值的方法
                    df = df.with_columns(pl.col(col).forward_fill().backward_fill())
                    # 对于剩余的空值，用中位数填充
                    df = df.with_columns(pl.col(col).fill_null(pl.col(col).median()))
                    
            except Exception as e:
                DATA_LOGGER.warning(f'处理列{col}时出错: {e}')
                continue
                
        return df
        
    except Exception as e:
        DATA_LOGGER.error(f'处理缺失值时出错: {e}')
        return df

def process_time_series_norm(df: pl.DataFrame, window_size: int = 20) -> pl.DataFrame:
    """使用滚动窗口为每个符号进行时间序列标准化"""
    try:
        feature_columns = [col for col in df.columns if col not in ['datetime', 'vt_symbol', 'symbol', 'open', 'high', 'low', 'close', 'volume', 'open_interest']]
        
        for col in feature_columns:
            if col in df.columns:
                try:
                    # 为每个符号计算滚动平均值和标准差 - 兼容性处理
                    try:
                        df = df.with_columns([
                            pl.col(col).rolling_mean(window_size).over(["vt_symbol"]).alias(f"{col}_mean"),
                            pl.col(col).rolling_std(window_size).over(["vt_symbol"]).alias(f"{col}_std")
                        ])
                    except (TypeError, AttributeError):
                        # 尝试不带参数名的版本
                        df = df.with_columns([
                            pl.col(col).rolling_mean(window_size).over(["vt_symbol"]).alias(f"{col}_mean"),
                            pl.col(col).rolling_std(window_size).over(["vt_symbol"]).alias(f"{col}_std")
                        ])
                    
                    # 应用z-score标准化
                    df = df.with_columns(
                        ((pl.col(col) - pl.col(f"{col}_mean")) / (pl.col(f"{col}_std") + 1e-8)).alias(col)
                    ).drop([f"{col}_mean", f"{col}_std"])
                except Exception as e:
                    DATA_LOGGER.warning(f'标准化列{col}时出错: {e}')
                    continue
        
        return df
        
    except Exception as e:
        DATA_LOGGER.error(f'时间序列标准化时出错: {e}')
        return df

def process_winsorize(df: pl.DataFrame, lower_quantile: float = 0.01, upper_quantile: float = 0.99) -> pl.DataFrame:
    """温莎化数据以减少异常值的影响"""
    try:
        feature_columns = [col for col in df.columns if col not in ['datetime', 'vt_symbol', 'symbol', 'open', 'high', 'low', 'close', 'volume', 'open_interest']]
        
        for col in feature_columns:
            if col in df.columns:
                try:
                    # 计算每个特征的分位数
                    lower_bound = df[col].quantile(lower_quantile)
                    upper_bound = df[col].quantile(upper_quantile)
                    
                    # 温莎化（将值限制在分位数阈值内）
                    df = df.with_columns(
                        pl.col(col).clip(lower_bound, upper_bound).alias(col)
                    )
                except Exception as e:
                    DATA_LOGGER.warning(f'温莎化列{col}时出错: {e}')
                    continue
        
        return df
        
    except Exception as e:
        DATA_LOGGER.error(f'温莎化处理时出错: {e}')
        return df

# =============================================================================
# 特征计算函数 - 与回测系统保持一致
# =============================================================================

def calculate_features(bar_df, symbol, lookback_window=200, target_features=None):
    """计算交易特征 - 与回测系统保持一致，支持目标特征列表"""
    if bar_df is None or len(bar_df) == 0:
        DATA_LOGGER.warning(f'输入数据为空: {symbol}')
        return None
        
    if len(bar_df) < lookback_window:
        DATA_LOGGER.info(f'数据量不足以计算特征, 需要至少{lookback_window}根K线, 当前{len(bar_df)}根')
        return None
    
    try:
        # 将DataFrame转换为polars DataFrame
        if isinstance(bar_df, pd.DataFrame):
            # 确保必要的列存在
            required_cols = ['datetime', 'open', 'high', 'low', 'close']
            missing_cols = [col for col in required_cols if col not in bar_df.columns]
            if missing_cols:
                DATA_LOGGER.error(f'缺少必要的列: {missing_cols}')
                return None
            
            # 检查数据类型
            for col in ['open', 'high', 'low', 'close']:
                if col in bar_df.columns:
                    bar_df[col] = pd.to_numeric(bar_df[col], errors='coerce')
            
            # 删除包含NaN的行
            bar_df = bar_df.dropna(subset=['open', 'high', 'low', 'close'])
            
            if len(bar_df) < lookback_window:
                DATA_LOGGER.warning(f'清理数据后长度不足: {len(bar_df)}')
                return None
            
            pl_df = pl.from_pandas(bar_df)
        else:
            pl_df = bar_df
        
        # 确保有vt_symbol列
        if 'vt_symbol' not in pl_df.columns:
            pl_df = pl_df.with_columns(pl.lit(symbol).alias('vt_symbol'))
        
        # 验证数据质量
        for col in ['open', 'high', 'low', 'close']:
            if col in pl_df.columns:
                if pl_df[col].null_count() > 0:
                    DATA_LOGGER.warning(f'列{col}包含空值: {pl_df[col].null_count()}个')
        
        result_df = pl_df.clone()
        
        # 如果提供了目标特征列表，优先计算这些特征
        if target_features:
            # 根据目标特征确定需要计算的窗口
            windows_needed = set()
            for feat in target_features:
                # 提取窗口大小
                import re
                match = re.search(r'_(\d+)$', feat)
                if match:
                    windows_needed.add(int(match.group(1)))
            
            # 如果没有找到窗口，使用默认窗口
            if not windows_needed:
                windows_needed = {1, 2, 3, 5, 10, 20, 30, 60}
        else:
            windows_needed = {1, 2, 3, 5, 10, 20, 30, 60}
        
        # 基本价格特征 - 根据目标特征动态计算
        for w in windows_needed:
            if w <= 30 or not target_features or any(f"_{w}" in feat for feat in target_features):
                
                # 收益率特征
                if not target_features or any(f'ret_{w}' in feat for feat in target_features):
                    result_df = result_df.with_columns(
                        (pl.col('close') / pl.col('close').shift(w) - 1).alias(f'ret_{w}')
                    )
                
                # 移动平均线特征
                if not target_features or any(f'ma_{w}' in feat for feat in target_features):
                    result_df = result_df.with_columns(
                        (pl.col('close').rolling_mean(w) / pl.col('close')).alias(f'ma_{w}')
                    )
                
                # 高低点特征
                if not target_features or any(f'high_ratio_{w}' in feat or f'low_ratio_{w}' in feat for feat in target_features):
                    result_df = result_df.with_columns(
                        (pl.col('high').rolling_max(w) / pl.col('close')).alias(f'high_ratio_{w}'),
                        (pl.col('close') / pl.col('low').rolling_min(w)).alias(f'low_ratio_{w}')
                    )
                
                # 价格波动特征
                if not target_features or any(f'std_{w}' in feat for feat in target_features):
                    result_df = result_df.with_columns(
                        (pl.col('close').rolling_std(w) / pl.col('close')).alias(f'std_{w}')
                    )
                
                # 价格位置特征
                if not target_features or any(f'price_position_{w}' in feat for feat in target_features):
                    result_df = result_df.with_columns(
                        ((pl.col('close') - pl.col('low').rolling_min(w)) / 
                        (pl.col('high').rolling_max(w) - pl.col('low').rolling_min(w) + 1e-6)).alias(f'price_position_{w}')
                    )
                
                # 价格与移动平均线的比率
                if not target_features or any(f'close_to_ma_{w}' in feat for feat in target_features):
                    result_df = result_df.with_columns(
                        (pl.col('close') / pl.col('close').rolling_mean(w)).alias(f'close_to_ma_{w}')
                    )
        
        # K线形态特征 - 仅在目标特征包含时计算
        if not target_features or any(feat in ['body_ratio', 'upper_shadow', 'lower_shadow'] for feat in target_features):
            if 'open' in result_df.columns:
                result_df = result_df.with_columns(
                    ((pl.col('close') - pl.col('open')) / (pl.col('high') - pl.col('low') + 1e-6)).alias('body_ratio'),
                    ((pl.col('high') - pl.max_horizontal([pl.col('open'), pl.col('close')])) / (pl.col('high') - pl.col('low') + 1e-6)).alias('upper_shadow'),
                    ((pl.min_horizontal([pl.col('open'), pl.col('close')]) - pl.col('low')) / (pl.col('high') - pl.col('low') + 1e-6)).alias('lower_shadow')
                )
        
        # 波动范围指标
        if not target_features or 'daily_range' in target_features:
            result_df = result_df.with_columns(
                ((pl.col('high') - pl.col('low')) / pl.col('open')).alias('daily_range')
            )
        
        # 动量指标 - 根据目标特征选择性计算
        for w in [5, 10, 20]:
            if w in windows_needed:
                # RSI指标
                if not target_features or any(f'rsi_{w}' in feat for feat in target_features):
                    result_df = result_df.with_columns(
                        (100 * (pl.col('close') > pl.col('close').shift(1)).rolling_mean(w)).alias(f'rsi_{w}')
                    )
                
                # MACD相关指标
                if not target_features or any(f'ema_' in feat or f'macd_{w}' in feat for feat in target_features):
                    result_df = result_df.with_columns(
                        (pl.col('close').rolling_mean(w//2) / pl.col('close')).alias(f'ema_short_{w}'),
                        (pl.col('close').rolling_mean(w) / pl.col('close')).alias(f'ema_long_{w}'),
                        (pl.col('close').rolling_mean(w//2) / pl.col('close').rolling_mean(w) - 1).alias(f'macd_{w}')
                    )
        
        # 波动率指标
        for w in [5, 10, 20]:
            if w in windows_needed:
                # ATR指标
                if not target_features or any(f'atr_{w}' in feat for feat in target_features):
                    result_df = result_df.with_columns(
                        ((pl.col('high') - pl.col('low')) / pl.col('close')).rolling_mean(w).alias(f'atr_{w}')
                    )
                
                # 波动率突破指标
                if not target_features or any(f'volatility_breakout_{w}' in feat for feat in target_features):
                    result_df = result_df.with_columns(
                        ((pl.col('close') - pl.col('close').rolling_mean(w)) / (pl.col('close').rolling_std(w) + 1e-6)).alias(f'volatility_breakout_{w}')
                    )
        
        # 成交量特征 (如果存在且在目标特征中)
        if 'volume' in result_df.columns:
            for w in [5, 10, 20]:
                if w in windows_needed:
                    # 成交量相关特征
                    volume_features_needed = [f'vol_ma_{w}', f'vol_ratio_{w}', f'vol_std_{w}', f'vol_std_orig_{w}', f'volume_price_corr_{w}']
                    if not target_features or any(feat in target_features for feat in volume_features_needed):
                        result_df = result_df.with_columns(
                            (pl.col('volume') / pl.col('volume').rolling_mean(w)).alias(f'vol_ma_{w}'),
                            (pl.col('volume').rolling_mean(w) / pl.col('volume')).alias(f'vol_ratio_{w}'),
                            (pl.col('volume').rolling_std(w) / pl.col('volume').rolling_mean(w)).alias(f'vol_std_{w}'),
                            (pl.col('volume').rolling_std(w) / pl.col('volume')).alias(f'vol_std_orig_{w}')
                        )
                        
                        # 尝试使用rolling_corr，如果不支持则跳过
                        try:
                            result_df = result_df.with_columns(
                                (pl.col('close').rolling_corr(pl.col('volume'), w)).alias(f'volume_price_corr_{w}')
                            )
                        except AttributeError:
                            # 如果rolling_corr不支持，使用替代方法或跳过
                            DATA_LOGGER.warning(f"rolling_corr not supported in this Polars version, skipping volume_price_corr_{w}")
                            # 可以使用简化的替代特征
                            result_df = result_df.with_columns(
                                ((pl.col('close').rolling_mean(w) * pl.col('volume').rolling_mean(w)) / 
                                 (pl.col('close').rolling_std(w) * pl.col('volume').rolling_std(w) + 1e-8)).alias(f'volume_price_corr_{w}')
                            )
        
        # 交易量价比特征
        if 'volume' in result_df.columns and (not target_features or 'turnover_ratio' in target_features):
            result_df = result_df.with_columns(
                (pl.col('volume') * pl.col('close') / (pl.col('volume') * pl.col('close') + 1e-6)).alias('turnover_ratio')
            )
        
        # 趋势强度指标
        for w in [5, 10, 20, 30, 60]:
            if w in windows_needed:
                # 上涨天数比例
                if not target_features or any(f'up_days_ratio_{w}' in feat for feat in target_features):
                    result_df = result_df.with_columns(
                        ((pl.col('close') > pl.col('close').shift(1)).rolling_sum(w) / w).alias(f'up_days_ratio_{w}')
                    )
                
                # 趋势方向指标
                if not target_features or any(f'adx_{w}' in feat for feat in target_features):
                    result_df = result_df.with_columns(
                        (((pl.col('close') > pl.col('close').shift(1)).rolling_sum(w) / w - 0.5) * 2).abs().alias(f'adx_{w}')
                    )
                
                # 分位数特征 - 确保所有窗口都能计算
                if len(result_df) >= w and (not target_features or any(f'qtlu_{w}' in feat or f'qtll_{w}' in feat for feat in target_features)):
                    try:
                        # 尝试使用quantile参数的新版本语法
                        result_df = result_df.with_columns(
                            (pl.col('close').rolling_quantile(quantile=0.8, window_size=w) / pl.col('close')).alias(f'qtlu_{w}'),
                            (pl.col('close').rolling_quantile(quantile=0.2, window_size=w) / pl.col('close')).alias(f'qtll_{w}')
                        )
                    except (TypeError, AttributeError) as e:
                        try:
                            # 尝试使用位置参数的旧版本语法
                            result_df = result_df.with_columns(
                                (pl.col('close').rolling_quantile(w, 0.8) / pl.col('close')).alias(f'qtlu_{w}'),
                                (pl.col('close').rolling_quantile(w, 0.2) / pl.col('close')).alias(f'qtll_{w}')
                            )
                        except (TypeError, AttributeError):
                            # 如果quantile不支持，使用max/min作为替代
                            DATA_LOGGER.warning(f"rolling_quantile not supported, using max/min for window {w}")
                            result_df = result_df.with_columns(
                                (pl.col('close').rolling_max(w) / pl.col('close')).alias(f'qtlu_{w}'),
                                (pl.col('close').rolling_min(w) / pl.col('close')).alias(f'qtll_{w}')
                            )
                else:
                    if len(result_df) < w:
                        DATA_LOGGER.debug(f"数据不足，跳过 qtlu_{w}, qtll_{w} (需要:{w}, 有:{len(result_df)})")
                    
                # 添加resi特征 - 线性回归残差
                if len(result_df) >= w and (not target_features or any(f'resi_{w}' in feat for feat in target_features)):
                    try:
                        # 使用Polars的滚动线性回归残差
                        # 这里使用一个简化的方法：价格相对于移动平均线的偏差
                        result_df = result_df.with_columns(
                            ((pl.col('close') - pl.col('close').rolling_mean(w)) / pl.col('close')).alias(f'resi_{w}')
                        )
                        print(f"✅ [DEBUG] resi_{w} 特征生成完成")
                    except Exception as e:
                        # 备用方法：使用价格趋势残差
                        try:
                            # 计算线性趋势的残差（更复杂但更准确的方法）
                            result_df = result_df.with_columns([
                                # 先计算窗口内的线性趋势
                                pl.col('close').rolling_mean(w).alias(f'_trend_{w}'),
                            ])
                            result_df = result_df.with_columns(
                                ((pl.col('close') - pl.col(f'_trend_{w}')) / pl.col('close')).alias(f'resi_{w}')
                            )
                            # 删除临时列
                            result_df = result_df.drop(f'_trend_{w}')
                            print(f"✅ [DEBUG] resi_{w} 使用备用方法生成")
                        except Exception as e2:
                            DATA_LOGGER.warning(f"无法计算resi_{w}: {e2}")
                            # 最后的备用方案：使用价格波动率
                            result_df = result_df.with_columns(
                                (pl.col('close').rolling_std(w) / pl.col('close')).alias(f'resi_{w}')
                            )
                            print(f"⚠️ [DEBUG] resi_{w} 使用标准差替代生成")
                else:
                    if len(result_df) < w:
                        print(f"⚠️ [DEBUG] 数据不足，跳过 resi_{w} (需要:{w}, 有:{len(result_df)})")
                        
        # 处理缺失值
        result_df = advanced_process_missing_values(result_df)
        
        # 数据标准化
        result_df = process_time_series_norm(result_df)
        
        # 异常值处理
        result_df = process_winsorize(result_df)
        
        # === 验证特征完整性 ===
        try:
            generated_features = list(result_df.columns)
            # 移除非特征列  
            feature_columns = [col for col in generated_features if col not in ['datetime', 'vt_symbol', 'symbol', 'open', 'high', 'low', 'close', 'volume', 'open_interest']]
            
            # 如果有目标特征，检查是否都已生成
            if target_features:
                missing_target_features = set(target_features) - set(feature_columns)
                if missing_target_features:
                    print(f"⚠️ [DEBUG] 仍缺少目标特征: {list(missing_target_features)}")
                    DATA_LOGGER.warning(f"特征计算后仍缺少目标特征: {list(missing_target_features)}")
                else:
                    print(f"✅ [DEBUG] 所有目标特征都已生成")
            else:
                # 检查关键缺失特征
                required_features = ['qtlu_20', 'qtlu_30', 'qtlu_60', 'qtll_30', 'qtll_60', 'resi_30', 'resi_60']
                missing_critical = [f for f in required_features if f not in feature_columns]
                
                if missing_critical:
                    print(f"⚠️ [DEBUG] 仍缺少关键特征: {missing_critical}")
                    DATA_LOGGER.warning(f"特征计算后仍缺少关键特征: {missing_critical}")
                else:
                    print(f"✅ [DEBUG] 所有关键特征都已生成")
                    
            # 输出前10个特征用于调试
            print(f"📋 [DEBUG] 生成的特征示例: {feature_columns[:10]}")
            
        except Exception as e:
            print(f"❌ [DEBUG] 特征验证失败: {e}")
        
        return result_df
    
    except Exception as e:
        DATA_LOGGER.error(f'计算特征时出错: {e}')
        import traceback
        traceback.print_exc()
        return None 



# =============================================================================
# 模型管理类
# =============================================================================

class ModelManager:
    """模型管理器 - 负责模型的加载、保存和更新"""
    def __init__(self, config, model_name=None):
        self.config = config
        self.models = {}
        self.feature_names = {}
        self.model_timestamps = {}
        self.model_name = model_name  # 用户指定的模型文件名
        
        # 合约映射配置：将实盘合约映射到训练合约
        self.contract_mapping = {
            # 黄金期货：所有au开头的合约都映射到au888
            'au2510': 'au888',
            'au2511': 'au888', 
            'au2512': 'au888',
            'au2601': 'au888',
            'au2602': 'au888',
            # 可以添加更多合约映射
            # 'ag2510': 'ag888',  # 银期货
            # 'cu2510': 'cu888',  # 铜期货
        }
        
        # 确保目录存在
        os.makedirs(config.model_dir, exist_ok=True)
        os.makedirs(config.feature_dir, exist_ok=True)
    
    def get_model_symbol(self, trading_symbol):
        """
        获取模型对应的合约代码
        
        Args:
            trading_symbol: 实盘交易合约，如 'au2510'
            
        Returns:
            str: 模型训练时使用的合约代码，如 'au888'
        """
        # 如果有直接映射，使用映射
        if trading_symbol in self.contract_mapping:
            return self.contract_mapping[trading_symbol]
        
        # 如果没有直接映射，尝试自动推断
        # 黄金合约：au开头的都映射到au888
        if trading_symbol.startswith('au') and len(trading_symbol) >= 4:
            return 'au888'
        
        # 银合约：ag开头的都映射到ag888
        if trading_symbol.startswith('ag') and len(trading_symbol) >= 4:
            return 'ag888'
            
        # 铜合约：cu开头的都映射到cu888
        if trading_symbol.startswith('cu') and len(trading_symbol) >= 4:
            return 'cu888'
        
        # 如果无法推断，返回原合约代码
        MODEL_LOGGER.warning(f"无法映射合约 {trading_symbol}，使用原始合约代码")
        return trading_symbol
    
    def load_model(self, symbol):
        """加载指定合约的模型，支持前缀模糊匹配和回测系统文件名兼容"""
        try:
            MODEL_LOGGER.info(f"开始加载模型 | 合约: {symbol} | 指定模型: {self.model_name}")
            
            # 获取模型对应的合约代码（支持合约映射）
            model_symbol = self.get_model_symbol(symbol)
            print(f"🔄 [DEBUG] 模型加载合约映射 | 原始: {symbol} | 映射后: {model_symbol}")
            
            # 优先用用户输入的模型名
            if self.model_name:
                model_path = os.path.join(self.config.model_dir, self.model_name)
                
                # 多种特征文件路径尝试
                feature_paths_to_try = [
                    os.path.join(self.config.feature_dir, f"{model_symbol}_features.pkl"),
                    os.path.join(self.config.feature_dir, f"selected_features_{model_symbol}_*.pkl"),
                    os.path.join(self.config.feature_dir, f"{symbol}_features.pkl")
                ]
                
                feature_path = None
                if os.path.exists(model_path):
                    # 寻找对应的特征文件
                    import glob
                    for path_pattern in feature_paths_to_try:
                        if '*' in path_pattern:
                            matches = glob.glob(path_pattern)
                            if matches:
                                feature_path = sorted(matches)[-1]  # 取最新的
                                break
                        else:
                            if os.path.exists(path_pattern):
                                feature_path = path_pattern
                                break
                    
                    if feature_path:
                        # 加载模型和特征
                        with open(model_path, 'rb') as f:
                            self.models[symbol] = pickle.load(f)
                        with open(feature_path, 'rb') as f:
                            self.feature_names[symbol] = pickle.load(f)
                        self.model_timestamps[symbol] = datetime.now()
                        MODEL_LOGGER.info(f"模型加载成功 | 合约: {symbol} | 模型路径: {model_path} | 特征文件: {feature_path} | 特征数量: {len(self.feature_names[symbol])}")
                        return True
                    else:
                        MODEL_LOGGER.warning(f"特征文件不存在 | 模型: {model_path} | 尝试的特征路径: {feature_paths_to_try}")
                else:
                    MODEL_LOGGER.warning(f"指定模型文件不存在 | 路径: {model_path}")
            
            # 自动查找模型文件（支持回测系统命名规范）
            import glob
            
            # 多种模型文件命名模式
            model_patterns = [
                os.path.join(self.config.model_dir, f"{model_symbol}_model.pkl"),                    # 标准格式
                os.path.join(self.config.model_dir, f"{model_symbol}_*.pkl"),                       # 带时间格式（回测系统）
                os.path.join(self.config.model_dir, f"{symbol}_model.pkl"),                         # 原始合约格式
                os.path.join(self.config.model_dir, f"{symbol}_*.pkl")                              # 原始合约带时间格式
            ]
            
            for model_pattern in model_patterns:
                if '*' in model_pattern:
                    model_files = glob.glob(model_pattern)
                    if model_files:
                        model_path = sorted(model_files)[-1]  # 取最新的模型文件
                        print(f"📊 [DEBUG] 找到模型文件 | 模式: {model_pattern} | 文件: {model_path}")
                        break
                else:
                    if os.path.exists(model_pattern):
                        model_path = model_pattern
                        print(f"📊 [DEBUG] 找到模型文件 | 路径: {model_path}")
                        break
            else:
                model_path = None
            
            if model_path:
                # 寻找对应的特征文件
                feature_patterns = [
                    os.path.join(self.config.feature_dir, f"selected_features_{model_symbol}_*.pkl"),  # 回测系统格式
                    os.path.join(self.config.feature_dir, f"{model_symbol}_*_features.pkl"),           # 标准带时间格式
                    os.path.join(self.config.feature_dir, f"{model_symbol}_features.pkl"),             # 标准格式
                    os.path.join(self.config.feature_dir, f"selected_features_{symbol}_*.pkl"),        # 原始合约格式
                    os.path.join(self.config.feature_dir, f"{symbol}_features.pkl")                    # 原始合约标准格式
                ]
                
                feature_path = None
                for feature_pattern in feature_patterns:
                    if '*' in feature_pattern:
                        feature_files = glob.glob(feature_pattern)
                        if feature_files:
                            feature_path = sorted(feature_files)[-1]  # 取最新的特征文件
                            print(f"📊 [DEBUG] 找到特征文件 | 模式: {feature_pattern} | 文件: {feature_path}")
                            break
                    else:
                        if os.path.exists(feature_pattern):
                            feature_path = feature_pattern
                            print(f"📊 [DEBUG] 找到特征文件 | 路径: {feature_path}")
                            break
                
                if feature_path:
                    # 加载模型和特征
                    with open(model_path, 'rb') as f:
                        self.models[symbol] = pickle.load(f)
                    with open(feature_path, 'rb') as f:
                        self.feature_names[symbol] = pickle.load(f)
                    self.model_timestamps[symbol] = datetime.now()
                    MODEL_LOGGER.info(f"自动模型加载成功 | 合约: {symbol} | 模型: {model_path} | 特征: {feature_path} | 特征数量: {len(self.feature_names[symbol])}")
                    return True
                else:
                    MODEL_LOGGER.warning(f"未找到对应的特征文件 | 模型: {model_path} | 尝试的特征模式: {feature_patterns}")

            # ====== 前缀模糊匹配逻辑 ======
            prefix_match = re.match(r'^[a-zA-Z]+', symbol)
            if prefix_match:
                prefix = prefix_match.group()
                MODEL_LOGGER.info(f"尝试前缀匹配 | 合约: {symbol} | 前缀: {prefix}")
                # 遍历模型目录，找第一个以prefix开头的模型文件
                model_files = [f for f in os.listdir(self.config.model_dir) if f.startswith(prefix) and f.endswith('.pkl')]
                feature_files = [f for f in os.listdir(self.config.feature_dir) if f.startswith(prefix) or f.startswith(f'selected_features_{prefix}')]
                
                if model_files and feature_files:
                    model_path = os.path.join(self.config.model_dir, model_files[0])
                    feature_path = os.path.join(self.config.feature_dir, feature_files[0])
                    with open(model_path, 'rb') as f:
                        self.models[symbol] = pickle.load(f)
                    with open(feature_path, 'rb') as f:
                        self.feature_names[symbol] = pickle.load(f)
                    self.model_timestamps[symbol] = datetime.now()
                    MODEL_LOGGER.info(f"前缀匹配成功 | 合约: {symbol} | 模型文件: {model_files[0]} | 特征文件: {feature_files[0]}")
                    return True
                else:
                    MODEL_LOGGER.warning(f"前缀匹配失败 | 前缀: {prefix} | 模型文件: {len(model_files)} | 特征文件: {len(feature_files)}")
            else:
                MODEL_LOGGER.warning(f"无法识别合约前缀 | 合约: {symbol}")
            return False
        except Exception as e:
            ERROR_LOGGER.error(f"模型加载异常 | 合约: {symbol} | 错误: {e}")
            return False
    
    def save_model(self, symbol, model, feature_names):
        """保存模型"""
        try:
            model_path = os.path.join(self.config.model_dir, f"{symbol}_model.pkl")
            feature_path = os.path.join(self.config.feature_dir, f"{symbol}_features.pkl")
            
            MODEL_LOGGER.info(f"开始保存模型 | 合约: {symbol} | 特征数量: {len(feature_names)}")
            
            # 保存模型
            with open(model_path, 'wb') as f:
                pickle.dump(model, f)
            
            # 保存特征名称
            with open(feature_path, 'wb') as f:
                pickle.dump(feature_names, f)
            
            self.models[symbol] = model
            self.feature_names[symbol] = feature_names
            self.model_timestamps[symbol] = datetime.now()
            
            MODEL_LOGGER.info(f"模型保存成功 | 合约: {symbol} | 模型路径: {model_path} | 特征路径: {feature_path}")
            return True
            
        except Exception as e:
            ERROR_LOGGER.error(f"模型保存失败 | 合约: {symbol} | 错误: {e}")
            return False
    
    def predict(self, symbol, features_df):
        """使用模型进行预测 - 动态适配特征"""
        start_time = time.time()  # 在函数开始就定义start_time
        try:
            print(f"🔍 [DEBUG] 开始模型预测 | 合约: {symbol}")
            
            # 合约映射：将具体合约映射到训练时使用的合约
            model_symbol = self.get_model_symbol(symbol)
            print(f"🔄 [DEBUG] 合约映射 | 原始: {symbol} | 映射后: {model_symbol}")
            
            if model_symbol not in self.models:
                print(f"❌ [DEBUG] 模型不存在 | 合约: {model_symbol} | 已加载模型: {list(self.models.keys())}")
                MODEL_LOGGER.warning(f"模型预测失败 | 合约: {symbol} (映射:{model_symbol}) | 原因: 模型不存在")
                return None
            
            if model_symbol not in self.feature_names:
                print(f"❌ [DEBUG] 特征名称不存在 | 合约: {model_symbol} | 已加载特征: {list(self.feature_names.keys())}")
                MODEL_LOGGER.warning(f"模型预测失败 | 合约: {symbol} (映射:{model_symbol}) | 原因: 特征名称不存在")
                return None
            
            print(f"✅ [DEBUG] 模型和特征检查通过 | 合约: {model_symbol}")
            
            model = self.models[model_symbol]
            feature_names = self.feature_names[model_symbol]
            
            # 直接从模型获取期望的特征数量（更准确）
            try:
                if hasattr(model, 'num_feature'):
                    expected_feature_count = model.num_feature()
                elif hasattr(model, 'feature_name'):
                    expected_feature_count = len(model.feature_name())
                else:
                    expected_feature_count = len(feature_names)
                
                print(f"📊 [DEBUG] 模型期望特征数量: {expected_feature_count}")
                print(f"📊 [DEBUG] 保存的特征数量: {len(feature_names)}")
                
                # 禁用特征替换逻辑，强制使用特征文件中的特征
                # 原逻辑：if hasattr(model, 'feature_name') and expected_feature_count != len(feature_names):
                print(f"✅ [DEBUG] 强制使用特征文件中的特征，不进行替换")
                    
            except Exception as e:
                print(f"⚠️ [DEBUG] 获取模型特征信息失败，使用保存的特征: {e}")
            
            # 智能特征选择和匹配
            if isinstance(features_df, pl.DataFrame):
                # 只选择模型需要且当前可用的特征
                available_features = [col for col in feature_names if col in features_df.columns]
                
                if len(available_features) != len(feature_names):
                    missing_features = set(feature_names) - set(available_features)
                    
                    # 如果缺少的特征数量不多，尝试动态生成或使用替代特征
                    if len(missing_features) <= 5:
                        print(f"🔧 [DEBUG] 尝试处理缺少的特征 | 缺少: {list(missing_features)}")
                        
                        # 对于缺少的特征，使用相似的特征或默认值
                        features_with_fallback = []
                        fallback_values = {}
                        
                        
                        
                        # 使用处理后的特征列表
                        try:
                            features_array = features_df.select(feature_names).to_numpy()
                            print(f"✅ [DEBUG] 特征转换成功(含回退) | 维度: {features_array.shape}")
                        except Exception as e:
                            print(f"❌ [DEBUG] 特征转换失败(含回退) | 错误: {e}")
                            return None
                    else:
                        print(f"❌ [DEBUG] 缺少特征过多 | 需要: {len(feature_names)} | 可用: {len(available_features)} | 缺少: {list(missing_features)[:10]}")
                        MODEL_LOGGER.warning(f"模型预测失败 | 合约: {symbol} | 原因: 缺少特征过多 | 缺少特征: {list(missing_features)[:5]}...")
                        return None
                else:
                    print(f"✅ [DEBUG] 特征完全匹配 | 合约: {symbol}")
                    # 按照训练时的特征顺序排列
                    try:
                        features_array = features_df.select(feature_names).to_numpy()
                        print(f"✅ [DEBUG] 特征转换成功 | 维度: {features_array.shape}")
                    except Exception as e:
                        print(f"❌ [DEBUG] 特征转换失败 | 错误: {e}")
                        return None
            else:
                print(f"🔄 [DEBUG] 处理Pandas DataFrame")
                # 对于Pandas，同样进行智能特征选择
                available_features = [col for col in feature_names if col in features_df.columns]
                
                if len(available_features) != len(feature_names):
                    missing_features = set(feature_names) - set(available_features)
                    
                    # 为缺少的特征添加默认值
                    for feat in missing_features:
                        features_df[feat] = 0.0
                        print(f"📌 [DEBUG] 为缺少特征 {feat} 添加默认值: 0")
                
                features_array = features_df[feature_names].values
                print(f"✅ [DEBUG] Pandas特征转换成功 | 维度: {features_array.shape}")
            
            # 预测
            print(f"🔮 [DEBUG] 开始LightGBM预测...")
            prediction_start = time.time()  # 记录预测开始时间
            # 使用LightGBM建议的参数来禁用形状检查，解决特征数量不匹配问题
            prediction = model.predict(features_array, predict_disable_shape_check=True)
            pred_time = time.time() - prediction_start
            
            result = prediction[-1] if len(prediction) > 0 else None
            
            total_time = time.time() - start_time
            MODEL_LOGGER.info(f"模型预测完成 | 合约: {symbol} | 预测值: {result:.6f} | 预测耗时: {pred_time:.3f}s | 总耗时: {total_time:.3f}s | 特征维度: {features_array.shape}")
            
            return result
            
        except Exception as e:
            total_time = time.time() - start_time
            print(f"❌ [DEBUG] 模型预测异常 | 合约: {symbol} | 错误: {e} | 耗时: {total_time:.3f}s")
            ERROR_LOGGER.error(f"模型预测异常 | 合约: {symbol} | 错误: {e} | 耗时: {total_time:.3f}s")
            import traceback
            print(f"🔍 [DEBUG] 异常堆栈: {traceback.format_exc()}")
            return None
    
    def find_fallback_feature(self, missing_feature, available_features):
        """寻找缺失特征的替代特征"""
        try:
            # 特征替代规则：相似窗口的特征可以互相替代
            import re
            
            # 提取特征的基础名称和窗口大小
            match = re.match(r'(.+)_(\d+)$', missing_feature)
            if match:
                base_name, window = match.groups()
                window = int(window)
                
                # 1. 寻找相同基础名称但不同窗口的特征
                for feat in available_features:
                    if feat.startswith(base_name + '_') and feat != missing_feature:
                        return feat
                
                # 2. 寻找相似的特征（例如 qtlu_30 可以用 qtlu_20 替代）
                similar_features = {
                    'qtlu': ['qtlu', 'qtll'],  # 分位数特征可以互相替代
                    'qtll': ['qtll', 'qtlu'],
                    'resi': ['std', 'volatility_breakout'],  # 残差可以用波动率替代
                    'std': ['atr', 'volatility_breakout'],   # 标准差可以用ATR替代
                    'atr': ['std', 'volatility_breakout'],   # ATR可以用标准差替代
                }
                
                if base_name in similar_features:
                    for similar_base in similar_features[base_name]:
                        for feat in available_features:
                            if feat.startswith(similar_base + '_'):
                                return feat
            
            # 3. 特殊情况的替代规则
            fallback_rules = {
                'ma_1': ['ret_1', 'close_to_ma_1'],
                'daily_range': ['atr_5', 'std_5'],
                'turnover_ratio': ['vol_std_5', 'vol_ratio_5'],
                'body_ratio': ['daily_range', 'atr_5'],
                'upper_shadow': ['daily_range', 'atr_5'],
                'lower_shadow': ['daily_range', 'atr_5'],
            }
            
            if missing_feature in fallback_rules:
                for fallback in fallback_rules[missing_feature]:
                    if fallback in available_features:
                        return fallback
            
            return None
            
        except Exception as e:
            print(f"⚠️ [DEBUG] 寻找替代特征失败: {e}")
            return None
    
    def retrain_model(self, symbol, training_data):
        """重新训练模型"""
        try:
            if training_data is None or len(training_data) < 100:
                LEARNING_LOGGER.warning(f"训练数据不足: {symbol}")
                return False
            
            # 准备训练数据
            feature_columns = [col for col in training_data.columns 
                             if col not in ['datetime', 'vt_symbol', 'symbol', 'open', 'high', 'low', 'close', 'volume', 'open_interest']]
            
            if len(feature_columns) == 0:
                LEARNING_LOGGER.error(f"没有可用特征: {symbol}")
                return False
            
            # 计算目标变量（未来3期收益率）
            training_data = training_data.with_columns(
                ((pl.col('close').shift(-self.config.predict_window) / pl.col('close') - 1) * 100).alias('target')
            )
            
            # 删除缺失值
            training_data = training_data.drop_nulls()
            
            if len(training_data) < 50:
                LEARNING_LOGGER.warning(f"清理后训练数据不足: {symbol}")
                return False
            
            # 准备特征和标签
            X = training_data.select(feature_columns).to_numpy()
            y = training_data['target'].to_numpy()
            
            # 时间序列分割
            tscv = TimeSeriesSplit(n_splits=self.config.validation_window)
            
            # 训练LightGBM模型
            lgb_params = getattr(self.config, 'lgb_params', {
                'objective': 'regression',
                'metric': 'rmse',
                'boosting_type': 'gbdt',
                'num_leaves': 31,
                'learning_rate': 0.05,
                'verbose': -1,
                'random_state': 42
            })
            model = lgb.LGBMRegressor(**lgb_params)
            
            # 交叉验证
            cv_scores = []
            for train_idx, val_idx in tscv.split(X):
                X_train, X_val = X[train_idx], X[val_idx]
                y_train, y_val = y[train_idx], y[val_idx]
                
                model.fit(X_train, y_train)
                y_pred = model.predict(X_val)
                score = mean_squared_error(y_val, y_pred)
                cv_scores.append(score)
            
            avg_cv_score = np.mean(cv_scores)
            
            # 检查模型改善
            if symbol in self.models:
                # 与现有模型比较
                old_predictions = self.models[symbol].predict(X[-100:])  # 使用最近100个样本
                old_score = mean_squared_error(y[-100:], old_predictions)
                
                improvement = (old_score - avg_cv_score) / old_score
                if improvement < self.config.min_improvement:
                    LEARNING_LOGGER.info(f"模型改善不足: {symbol}, 改善: {improvement:.2%}")
                    return False
            
            # 使用全部数据重新训练
            model.fit(X, y)
            
            # 保存模型
            success = self.save_model(symbol, model, feature_columns)
            
            if success:
                LEARNING_LOGGER.info(f"模型重训练成功: {symbol}, CV得分: {avg_cv_score:.4f}")
            
            return success
            
        except Exception as e:
            LEARNING_LOGGER.error(f"重训练模型失败: {symbol}, 错误: {e}")
            return False




# =============================================================================
# 主程序入口
# =============================================================================

def main():
    """主程序入口"""
    print("="*50)
    print("LightGBM期货策略实盘交易系统（增强版）")
    print("版本: 2.1 - 与回测系统保持一致")
    print("="*50)
    
    try:
        SYSTEM_LOGGER.info(f"系统启动 | 版本: 2.1 | 启动时间: {datetime.now()}")
        
        # 导入必要的库
        from multiprocessing import Process, Queue
        from AlgoPlus.CTP.MdApi import run_tick_engine
        from AlgoPlus.CTP.FutureAccount import get_simulate_account, FutureAccount
        
        SYSTEM_LOGGER.info("依赖库导入成功")
        
        # 创建临时工作目录
        timestamp = int(time.time())
        temp_md_dir = f"./log/user_md_{timestamp}"
        temp_td_dir = f"./log/user_td_{timestamp}"
        
        # 确保log目录存在
        os.makedirs('./log', exist_ok=True)
        os.makedirs(temp_md_dir, exist_ok=True)
        os.makedirs(temp_td_dir, exist_ok=True)
        
        SYSTEM_LOGGER.info(f"工作目录创建完成 | MD目录: {temp_md_dir} | TD目录: {temp_td_dir}")
        
        # 用户输入模型/特征品种代码和实盘交易合约代码
        model_symbol = input("请输入模型/特征的品种代码（如 au888）: ").strip()
        trade_symbol = input("请输入实盘交易的合约代码（如 au2510）: ").strip()
        
        SYSTEM_LOGGER.info(f"用户输入 | 模型品种: {model_symbol} | 交易合约: {trade_symbol}")
        
        symbols = [trade_symbol]
        
        # 从配置文件读取SimNow账户信息
        config = StrategyConfig()
        SYSTEM_LOGGER.info(f"配置加载完成 | 配置文件路径: {config.__class__.__name__}")
        
        # 配置
        investor_id = config.simnow_account['user_id']
        password = config.simnow_account['password']
        server_name = config.simnow_account['server_name']
        
        # 检查账户信息是否配置
        if not investor_id or not password:
            error_msg = "SimNow账户信息未配置！请在strategy_config.py中配置simnow_account的user_id和password"
            SYSTEM_LOGGER.error(error_msg)
            ERROR_LOGGER.error(error_msg)
            print("⚠ " + error_msg)
            return
               
        # 确保订阅列表格式正确
        subscribe_list = []
        for s in symbols:
            if isinstance(s, str):
                subscribe_list.append(s.encode('utf-8'))
            else:
                subscribe_list.append(s)
        
        SYSTEM_LOGGER.info(f"订阅合约准备 | 合约列表: {[s.decode() if isinstance(s, bytes) else s for s in subscribe_list]}")
        print(f"订阅合约列表: {[s.decode() if isinstance(s, bytes) else s for s in subscribe_list]}")
        
        # 调用方式
        future_account = get_simulate_account(
            investor_id=investor_id,
            password=password,
            server_name=server_name,
            subscribe_list=subscribe_list,
        )
        
        SYSTEM_LOGGER.info(f"SimNow账户创建成功 | 订阅合约数量: {len(future_account.subscribe_list)}")
        print(f"开始交易，订阅合约数量: {len(future_account.subscribe_list)}")
        print(f"账户信息: 用户ID={future_account.investor_id}, 服务器={config.simnow_account['server_name']}")
        
        # 创建共享队列
        share_queue = Queue(maxsize=200)
        
        SYSTEM_LOGGER.info("共享队列创建完成，准备启动进程")
        
        # 启动行情进程
        md_process = Process(target=run_tick_engine, args=(future_account, [share_queue]))
        
        # 启动交易进程，使用正确的参数顺序
        trader_process = Process(target=run_trader, args=(
            future_account.broker_id,
            future_account.server_dict['TDServer'],
            future_account.investor_id,
            future_account.password,
            future_account.app_id,
            future_account.auth_code,
            share_queue,
            future_account.td_flow_path,  # 使用 future_account.td_flow_path 而不是 temp_td_dir
            2,  # private_resume_type
            2,  # public_resume_type
            symbols,  # symbols 参数
            model_symbol,  # model_symbol 参数
            trade_symbol   # trade_symbol 参数
        ))
        
        # 启动进程
        print("正在启动行情进程...")
        SYSTEM_LOGGER.info("开始启动行情进程")
        md_process.start()
        SYSTEM_LOGGER.info(f"行情进程启动成功 | PID: {md_process.pid}")
        print(f"行情进程已启动，PID: {md_process.pid}")
        
        print("正在启动交易进程...")
        SYSTEM_LOGGER.info("开始启动交易进程")
        trader_process.start()
        SYSTEM_LOGGER.info(f"交易进程启动成功 | PID: {trader_process.pid}")
        print(f"交易进程已启动，PID: {trader_process.pid}")
        
        print("交易系统已启动，按Ctrl+C退出")
        SYSTEM_LOGGER.info("交易系统启动完成，等待用户操作")
        
        # 检查当前时间
        current_time = datetime.now()
        print(f"当前时间: {current_time.strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 简单的交易时间检查（黄金期货大致时间）
        hour = current_time.hour
        minute = current_time.minute
        current_hm = hour * 100 + minute
        
        # 黄金期货交易时间（大致）：
        # 日盘：09:00-10:15, 10:30-11:30, 13:30-15:00
        # 夜盘：21:00-02:30
        is_trading_time = (
            (900 <= current_hm <= 1015) or 
            (1030 <= current_hm <= 1130) or 
            (1330 <= current_hm <= 1500) or 
            (2100 <= current_hm <= 2359) or 
            (0 <= current_hm <= 230)
        )
        
        if is_trading_time:
            SYSTEM_LOGGER.info("当前在交易时间内")
            print("✓ 当前在交易时间内")
        else:
            SYSTEM_LOGGER.warning("当前不在交易时间内")
            print("⚠ 当前不在交易时间内，可能收不到行情数据")
        
        # 监控进程状态
        time.sleep(2)  # 等待进程启动
        md_alive = md_process.is_alive()
        trader_alive = trader_process.is_alive()
        
        SYSTEM_LOGGER.info(f"进程状态检查 | 行情进程: {'运行中' if md_alive else '已停止'} | 交易进程: {'运行中' if trader_alive else '已停止'}")
        print(f"行情进程状态: {'运行中' if md_alive else '已停止'}")
        print(f"交易进程状态: {'运行中' if trader_alive else '已停止'}")
        
        # 等待一段时间看是否有行情数据
        print("等待行情数据...")
        time.sleep(5)
        
        
        # 等待进程结束
        try:
            SYSTEM_LOGGER.info("系统进入运行状态，等待进程结束或用户中断")
            md_process.join()
            trader_process.join()
        except KeyboardInterrupt:
            SYSTEM_LOGGER.info("收到用户中断信号，开始关闭系统")
            print("\n收到退出信号，正在关闭交易系统...")
            md_process.terminate()
            trader_process.terminate()
            md_process.join()
            trader_process.join()
            SYSTEM_LOGGER.info("交易系统关闭完成")
            print("交易系统已关闭")
            
    except Exception as e:
        error_msg = f"主程序异常: {e}"
        SYSTEM_LOGGER.error(error_msg)
        ERROR_LOGGER.error(error_msg)
        print(error_msg)
        import traceback
        traceback.print_exc()

def run_trader(broker_id, td_server, investor_id, password, app_id, auth_code, md_queue=None, page_dir='', private_resume_type=2, public_resume_type=2, symbols=None, model_symbol=None, trade_symbol=None):
    """运行交易程序"""
    try:
        # 创建必要的目录
        import os
        os.makedirs('./log', exist_ok=True)
        os.makedirs('./models', exist_ok=True)
        os.makedirs('./data', exist_ok=True)
        os.makedirs('./traderdata', exist_ok=True)
        os.makedirs('./logs', exist_ok=True)
        
        # 创建MyTrader实例
        my_trader = MyTrader(
            broker_id, 
            td_server, 
            investor_id, 
            password, 
            app_id, 
            auth_code, 
            md_queue, 
            page_dir, 
            private_resume_type, 
            public_resume_type
        )
        
        # 立即初始化配置对象，因为后面需要使用
        my_trader.config = StrategyConfig()
        my_trader.model_manager = ModelManager(my_trader.config)
        SIGNAL_LOGGER.info("配置对象初始化完成")

        # 设置模型相关参数
        if model_symbol:
            my_trader.model_symbol = model_symbol
        if trade_symbol:
            my_trader.trade_symbol = trade_symbol
            
        # 设置交易合约列表
        if symbols:
            my_trader.symbols = symbols
            
        # 加载模型
        if model_symbol and not my_trader.model_manager.load_model(model_symbol):
            SIGNAL_LOGGER.warning(f"模型加载失败: {model_symbol}，将不使用模型交易")
        
        # 设置交易合约列表和持仓信息（移到这里，不需要 start_trading）
        my_trader.symbols = symbols or []
        for symbol in my_trader.symbols:
            if symbol not in my_trader.current_positions:
                my_trader.current_positions[symbol] = 0
            if symbol not in my_trader.stop_order_dict:
                my_trader.stop_order_dict[symbol] = {
                    'long': {'position': 0, 'entry_price': 0, 'stop_loss': 0, 'take_profit': 0, 'trailing_stop': 0},
                    'short': {'position': 0, 'entry_price': 0, 'stop_loss': 0, 'take_profit': 0, 'trailing_stop': 0}
                }
        
        # 标记系统为运行状态
        my_trader.is_running = True
        
        # 初始化持仓状态（在系统启动后立即执行）
        if symbols:
            try:
                my_trader.initialize_position_on_startup()
            except Exception as e:
                SIGNAL_LOGGER.error(f"初始化持仓状态失败: {e}")
        
        SIGNAL_LOGGER.info("交易系统准备完毕，开始主循环（AlgoPlus将自动连接）")
        my_trader.Join()
            
    except Exception as e:
        SIGNAL_LOGGER.error(f"运行交易程序失败: {e}")
        import traceback
        traceback.print_exc()

# =============================================================================
# MyTrader交易类 - 基于TraderApiBase
# =============================================================================

class MyTrader(TraderApiBase):
    def __init__(self, broker_id, td_server, investor_id, password, app_id, auth_code, md_queue=None, page_dir='', private_resume_type=2, public_resume_type=2):
        # Cython类不使用super().__init__()方式调用父类初始化方法
        # TraderApiBase.__init__会由Cython自动处理
        
        # 基本参数 - 
        self.py = 30  # 价格偏移，更容易成交
        self.cont_df = 0  # 数据计数器
        self.Lots = 1  # 下单手数
        self.clearing_executed = False
        self.day_closed = False  # 日内平仓标志
        self.kgdata = True  # 历史数据加载标志
        
        # 🔑 关键：全局持仓变量（参考o3mini.py）
        self.pos = 0  # 全局持仓状态：0=无持仓，正数=多头，负数=空头
        
        # 交易状态
        self.is_running = False
        self.status = -1  # -1表示未连接，0表示已连接可交易状态
        
        # 持仓管理
        self.current_positions = {}  # 当前持仓: {symbol: position}
        self.symbols = []  # 交易合约列表
        self.md_queue = md_queue   # 行情数据队列
        
        # K线数据缓存
        self.bar_data_cache = {}  # 存储K线数据缓存: {symbol: [bar_dict]}
        
        # CSV文件管理
        self.csv_data_cache = {}  # 存储CSV数据缓存: {symbol: DataFrame}
        self.csv_file_paths = {}  # CSV文件路径: {symbol: file_path}
        
        # 止损止盈字典，按合约ID索引
        self.stop_order_dict = {}
        
        # 延迟初始化配置对象，避免在构造函数中创建复杂对象
        self.config = None
        # 移除风险管理器属性
        # self.risk_manager = None  # 已删除
        
        # 账户信息属性 - 直接从CTP获取
        self.account_balance = 0.0      # 账户余额
        self.available_margin = 0.0     # 可用保证金
        

        self.model_manager = None
        
        # 模型相关
        self.model_name = None
        self.model_symbol = None
        self.trade_symbol = None
        
        # === 后台线程池，用于执行耗时的特征计算与ML预测 ===
        import concurrent.futures  # 延迟导入，避免在顶层占用资源
        self.executor = concurrent.futures.ThreadPoolExecutor(max_workers=2)
        self._ml_future = None  # 记录当前正在执行的ML任务
        
        # === 按照 vip23 脚本的方式，只设置必要的业务参数 ===
        self.py = 30  # 价格偏移，更容易成交
        
        SIGNAL_LOGGER.info("MyTrader初始化完成")
    
    # === AlgoPlus TraderApiBase 自动处理连接和登录，不需要重写这些回调 ===
    # 如果重写了连接相关回调可能会干扰自动连接流程，只保留交易相关回调
    
    def OnRtnTrade(self, pTrade):
        # 完全按照o3mini.py的方式
        print("||成交回报||", pTrade)
        
        # 获取成交信息
        instrument_id = pTrade['InstrumentID'].decode()
        direction = pTrade['Direction'].decode()  # '0'为买入，'1'为卖出
        offset_flag = pTrade['OffsetFlag'].decode()  # '0'为开仓，'1'为平仓，'3'为平今
        volume = pTrade['Volume']
        price = pTrade['Price']
        
        # 根据成交类型更新持仓信息
        if offset_flag in ['1', '3']:  # 平仓或平今
            # 判断平的是多头还是空头
            if direction == '1':  # 卖出，平多头
                print(f"平多头成交: {instrument_id}, 价格: {price}, 数量: {volume}")
                # 清空多头持仓信息
                self.clear_position_info(instrument_id, 'long')
                self.current_positions[instrument_id] = 0  # 更新ML交易使用的持仓状态
                POSITION_LOGGER.info(f"多头平仓成功 | 合约: {instrument_id} | 价格: {price} | 数量: {volume}")
                
                # 保存平仓记录
                try:
                    self.save_position_to_csv(instrument_id)
                    
                    # 保存成交记录
                    trade_record = {
                        'direction': '卖出',
                        'offset': '平仓',
                        'price': price,
                        'volume': volume,
                        'trade_id': str(price) + '_' + str(volume),
                        'order_ref': ''
                    }
                    self.save_trade_record_to_csv(instrument_id, trade_record)
                except:
                    pass
                    
            else:  # 买入，平空头
                print(f"平空头成交: {instrument_id}, 价格: {price}, 数量: {volume}")
                # 清空空头持仓信息
                self.clear_position_info(instrument_id, 'short')
                self.current_positions[instrument_id] = 0  # 更新ML交易使用的持仓状态
                POSITION_LOGGER.info(f"空头平仓成功 | 合约: {instrument_id} | 价格: {price} | 数量: {volume}")
                
                # 保存平仓记录
                try:
                    self.save_position_to_csv(instrument_id)
                    
                    # 保存成交记录
                    trade_record = {
                        'direction': '买入',
                        'offset': '平仓',
                        'price': price,
                        'volume': volume,
                        'trade_id': str(price) + '_' + str(volume),
                        'order_ref': ''
                    }
                    self.save_trade_record_to_csv(instrument_id, trade_record)
                except:
                    pass
        
        elif offset_flag == '0':  # 开仓
            if direction == '0':  # 买入，开多头
                print(f"开多头成交: {instrument_id}, 价格: {price}, 数量: {volume}")
                # 如果有空头持仓，先清空
                if instrument_id in self.stop_order_dict and self.stop_order_dict[instrument_id]['short']['position'] > 0:
                    self.clear_position_info(instrument_id, 'short')
                
                # 设置多头持仓信息
                sl_price = price * (1 - 0.02)  # 2%止损
                tp_price = price * (1 + 0.01)  # 1%止盈
                
                # 设置初始跟踪止损价
                initial_trailing_stop = price * (1 - 0.01)
                
                # 更新多头持仓信息
                self.update_stop_order_dict(instrument_id, 'long', volume, price, sl_price, tp_price, initial_trailing_stop)
                self.pos = volume  # 更新全局持仓状态
                self.current_positions[instrument_id] = volume  # 更新ML交易使用的持仓状态
                
                print(f"📈 [DEBUG] 多头持仓更新 | 合约: {instrument_id} | 持仓: {volume} | 价格: {price}")
                POSITION_LOGGER.info(f"多头开仓成功 | 合约: {instrument_id} | 持仓: {volume} | 价格: {price}")
                
                # 保存持仓信息
                try:
                    self.save_position_to_csv(instrument_id)
                    
                    # 保存成交记录
                    trade_record = {
                        'direction': '买入',
                        'offset': '开仓',
                        'price': price,
                        'volume': volume,
                        'trade_id': str(price) + '_' + str(volume),  # 简单的交易ID
                        'order_ref': ''
                    }
                    self.save_trade_record_to_csv(instrument_id, trade_record)
                except:
                    pass
                    
            else:  # 卖出，开空头
                print(f"开空头成交: {instrument_id}, 价格: {price}, 数量: {volume}")
                # 如果有多头持仓，先清空
                if instrument_id in self.stop_order_dict and self.stop_order_dict[instrument_id]['long']['position'] > 0:
                    self.clear_position_info(instrument_id, 'long')
                
                # 设置空头持仓信息
                sl_price = price * (1 + 0.02)  # 2%止损
                tp_price = price * (1 - 0.01)  # 1%止盈
                
                # 设置初始跟踪止损价
                initial_trailing_stop = price * (1 + 0.01)
                
                # 更新空头持仓信息
                self.update_stop_order_dict(instrument_id, 'short', volume, price, sl_price, tp_price, initial_trailing_stop)
                self.pos = -volume  # 更新全局持仓状态
                self.current_positions[instrument_id] = -volume  # 更新ML交易使用的持仓状态
                
                print(f"📉 [DEBUG] 空头持仓更新 | 合约: {instrument_id} | 持仓: {-volume} | 价格: {price}")
                POSITION_LOGGER.info(f"空头开仓成功 | 合约: {instrument_id} | 持仓: {-volume} | 价格: {price}")
                
                # 保存持仓信息
                try:
                    self.save_position_to_csv(instrument_id)
                    
                    # 保存成交记录
                    trade_record = {
                        'direction': '卖出',
                        'offset': '开仓',
                        'price': price,
                        'volume': volume,
                        'trade_id': str(price) + '_' + str(volume),  # 简单的交易ID
                        'order_ref': ''
                    }
                    self.save_trade_record_to_csv(instrument_id, trade_record)
                except:
                    pass
    
    def save_to_csv(self, symbol):
        """保存持仓信息到CSV（参考o3mini.py）"""
        try:
            import pandas as pd
            from datetime import datetime
            
            # 使用完整的合约代码
            symbol = str(symbol)
            
            # 初始化止损价格（如果不存在）
            if not hasattr(self, 'short_trailing_stop_price'):
                self.short_trailing_stop_price = 0.0
            if not hasattr(self, 'long_trailing_stop_price'):
                self.long_trailing_stop_price = 0.0
            if not hasattr(self, 'sl_long_price'):
                self.sl_long_price = 0.0
            if not hasattr(self, 'sl_shor_price'):
                self.sl_shor_price = 0.0
            
            # 创建DataFrame
            data = {
                'datetime': [datetime.now().strftime('%Y-%m-%d %H:%M:%S')],
                'pos': [self.pos],
                'short_trailing_stop_price': [self.short_trailing_stop_price],
                'long_trailing_stop_price': [self.long_trailing_stop_price],
                'sl_long_price': [self.sl_long_price],
                'sl_shor_price': [self.sl_shor_price],
            }
            
            df = pd.DataFrame(data)
            
            # 确保目录存在
            import os
            os.makedirs("traderdata", exist_ok=True)
            
            # 将DataFrame保存到CSV文件
            df.to_csv(f"traderdata/{symbol}traderdata.csv", index=False)
            
            print(f"✅ 持仓信息已保存 | 合约: {symbol} | 持仓: {self.pos}")
            POSITION_LOGGER.info(f"持仓信息已保存 | 合约: {symbol} | 持仓: {self.pos}")
            
        except Exception as e:
            ERROR_LOGGER.error(f"保存持仓信息失败 | 合约: {symbol} | 错误: {e}")
    
    def save_trading_data(self, instrument_id, trader_df):
        """保存交易数据到文件"""
        try:
            import pandas as pd
            import os
            from datetime import datetime
            
            # 确保数据目录存在
            os.makedirs("data", exist_ok=True)
            
            # 构建文件名
            filename = f"data/{instrument_id}_trading_data.csv"
            
            # 保存数据
            if isinstance(trader_df, pd.DataFrame) and len(trader_df) > 0:
                trader_df.to_csv(filename, index=False)
                DATA_LOGGER.info(f"交易数据已保存 | 合约: {instrument_id} | 数据量: {len(trader_df)} | 文件: {filename}")
            else:
                DATA_LOGGER.warning(f"无效的交易数据 | 合约: {instrument_id} | 数据类型: {type(trader_df)}")
                
        except Exception as e:
            ERROR_LOGGER.error(f"保存交易数据失败 | 合约: {instrument_id} | 错误: {e}")
    
    def initialize_csv_with_historical_data(self, instrument_id):
        """基于现有历史数据缓存初始化CSV文件"""
        try:
            import pandas as pd
            import os
            from datetime import datetime
            
            # 确保data目录存在
            os.makedirs("data", exist_ok=True)
            
            # CSV文件路径
            csv_filename = f"data/{instrument_id}_realtime_5m_with_prediction.csv"
            self.csv_file_paths[instrument_id] = csv_filename
            
            # 从现有的bar_data_cache获取历史数据
            if instrument_id in self.bar_data_cache and self.bar_data_cache[instrument_id]:
                historical_data = []
                
                for bar in self.bar_data_cache[instrument_id]:
                    historical_data.append({
                        'datetime': bar['datetime'],
                        'symbol': instrument_id,
                        'open': bar['open'],
                        'high': bar['high'], 
                        'low': bar['low'],
                        'close': bar['close'],
                        'volume': bar['volume'],
                        'ml_prediction': None  # 历史数据暂时没有预测值
                    })
                
                # 创建DataFrame
                self.csv_data_cache[instrument_id] = pd.DataFrame(historical_data)
                self.csv_data_cache[instrument_id]['datetime'] = pd.to_datetime(self.csv_data_cache[instrument_id]['datetime'])
                
                # 保存到文件
                self.csv_data_cache[instrument_id].to_csv(csv_filename, index=False)
                DATA_LOGGER.info(f"CSV文件已初始化 | 合约: {instrument_id} | 历史数据量: {len(self.csv_data_cache[instrument_id])} | 文件: {csv_filename}")
                print(f"✅ [DEBUG] CSV文件已初始化 | 合约: {instrument_id} | 历史数据量: {len(self.csv_data_cache[instrument_id])}")
                
            else:
                # 创建空的DataFrame
                self.csv_data_cache[instrument_id] = pd.DataFrame(columns=[
                    'datetime', 'symbol', 'open', 'high', 'low', 'close', 'volume', 'ml_prediction'
                ])
                self.csv_data_cache[instrument_id].to_csv(csv_filename, index=False)
                DATA_LOGGER.info(f"空CSV文件已创建 | 合约: {instrument_id} | 文件: {csv_filename}")
                print(f"✅ [DEBUG] 空CSV文件已创建 | 合约: {instrument_id}")
                
        except Exception as e:
            ERROR_LOGGER.error(f"初始化CSV文件失败 | 合约: {instrument_id} | 错误: {e}")
    
    def append_new_bar_to_csv(self, instrument_id, bar_data, ml_prediction=None):
        """追加新的5分钟K线数据到CSV文件"""
        try:
            import pandas as pd
            from datetime import datetime
            
            # 确保CSV已初始化
            if instrument_id not in self.csv_data_cache:
                self.initialize_csv_with_historical_data(instrument_id)
            
            # 准备新的行数据
            new_row = {
                'datetime': bar_data['datetime'],
                'symbol': instrument_id,
                'open': bar_data['open'],
                'high': bar_data['high'],
                'low': bar_data['low'], 
                'close': bar_data['close'],
                'volume': bar_data['volume'],
                'ml_prediction': ml_prediction
            }
            
            # 转换时间格式
            if isinstance(new_row['datetime'], str):
                new_row['datetime'] = pd.to_datetime(new_row['datetime'])
            
            # 检查是否是重复数据或更新现有K线
            if len(self.csv_data_cache[instrument_id]) > 0:
                last_datetime = self.csv_data_cache[instrument_id]['datetime'].iloc[-1]
                if new_row['datetime'] == last_datetime:
                    # 更新现有K线（同一时间的更新）
                    self.csv_data_cache[instrument_id].iloc[-1] = new_row
                    print(f"📊 [DEBUG] 更新现有K线数据 | 合约: {instrument_id} | 时间: {new_row['datetime']} | 预测值: {ml_prediction}")
                elif new_row['datetime'] > last_datetime:
                    # 添加新的K线数据
                    new_df = pd.DataFrame([new_row])
                    self.csv_data_cache[instrument_id] = pd.concat([self.csv_data_cache[instrument_id], new_df], ignore_index=True)
                    print(f"📊 [DEBUG] 添加新K线数据 | 合约: {instrument_id} | 时间: {new_row['datetime']} | 预测值: {ml_prediction}")
                else:
                    # 跳过旧数据
                    return
            else:
                # 第一条数据
                self.csv_data_cache[instrument_id] = pd.DataFrame([new_row])
                print(f"📊 [DEBUG] 添加首条K线数据 | 合约: {instrument_id} | 时间: {new_row['datetime']} | 预测值: {ml_prediction}")
            
            # 限制内存中的数据量（保留最近2000条）
            if len(self.csv_data_cache[instrument_id]) > 2000:
                self.csv_data_cache[instrument_id] = self.csv_data_cache[instrument_id].tail(2000).reset_index(drop=True)
            
            # 保存到文件
            csv_filename = self.csv_file_paths.get(instrument_id)
            if csv_filename:
                self.csv_data_cache[instrument_id].to_csv(csv_filename, index=False)
                DATA_LOGGER.info(f"CSV文件已更新 | 合约: {instrument_id} | 总数据量: {len(self.csv_data_cache[instrument_id])}")
                
        except Exception as e:
            ERROR_LOGGER.error(f"追加CSV数据失败 | 合约: {instrument_id} | 错误: {e}")
    
    def read_position_from_csv(self, symbol):
        """读取保存的持仓和交易数据 - 参照sp.py实现"""
        try:
            import pandas as pd
            import os
            import re
            
            # 使用正则表达式提取英文字母作为符号标识
            clean_symbol = ''.join(re.findall('[a-zA-Z]', str(symbol)))
            folder_path = "traderdata"
            file_path = os.path.join(folder_path, f"{clean_symbol}traderdata.csv")
            
            # 如果文件夹不存在则创建
            if not os.path.exists(folder_path):
                os.makedirs(folder_path)
                print(f"✅ [DEBUG] 创建traderdata文件夹")
            
            # 读取保留的交易数据CSV文件
            if os.path.exists(file_path):
                df = pd.read_csv(file_path)
                if not df.empty:
                    # 选择最后一行数据
                    row = df.iloc[-1]
                    
                    # 根据CSV文件的列名将数据赋值给相应的属性
                    recovered_pos = int(row['pos'])
                    recovered_long_entry = float(row.get('long_entry_price', 0))
                    recovered_short_entry = float(row.get('short_entry_price', 0))
                    recovered_long_stop = float(row.get('long_stop_loss', 0))
                    recovered_short_stop = float(row.get('short_stop_loss', 0))
                    recovered_long_target = float(row.get('long_take_profit', 0))
                    recovered_short_target = float(row.get('short_take_profit', 0))
                    recovered_datetime = row.get('datetime', '')
                    
                    # 更新当前持仓状态
                    self.current_positions[symbol] = recovered_pos
                    self.pos = recovered_pos  # 更新全局持仓状态
                    
                    # 恢复止损止盈字典
                    if symbol not in self.stop_order_dict:
                        self.stop_order_dict[symbol] = {
                            'long': {'position': 0, 'entry_price': 0, 'stop_loss': 0, 'take_profit': 0, 'trailing_stop': 0},
                            'short': {'position': 0, 'entry_price': 0, 'stop_loss': 0, 'take_profit': 0, 'trailing_stop': 0}
                        }
                    
                    if recovered_pos > 0:  # 多头持仓
                        self.stop_order_dict[symbol]['long'] = {
                            'position': recovered_pos,
                            'entry_price': recovered_long_entry,
                            'stop_loss': recovered_long_stop,
                            'take_profit': recovered_long_target,
                            'trailing_stop': recovered_long_stop
                        }
                        print(f"🔄 [DEBUG] 恢复多头持仓 | 合约: {symbol} | 持仓: {recovered_pos} | 开仓价: {recovered_long_entry} | 止损: {recovered_long_stop}")
                        
                    elif recovered_pos < 0:  # 空头持仓
                        self.stop_order_dict[symbol]['short'] = {
                            'position': abs(recovered_pos),
                            'entry_price': recovered_short_entry,
                            'stop_loss': recovered_short_stop,
                            'take_profit': recovered_short_target,
                            'trailing_stop': recovered_short_stop
                        }
                        print(f"🔄 [DEBUG] 恢复空头持仓 | 合约: {symbol} | 持仓: {recovered_pos} | 开仓价: {recovered_short_entry} | 止损: {recovered_short_stop}")
                    
                    print(f"✅ [DEBUG] 找到历史交易数据文件，已恢复持仓状态 | 合约: {symbol} | 持仓: {recovered_pos} | 时间: {recovered_datetime}")
                    POSITION_LOGGER.info(f"历史持仓状态已恢复 | 合约: {symbol} | 持仓: {recovered_pos}")
                    return True
                else:
                    print(f"⚠️ [DEBUG] 交易数据文件为空 | 文件: {file_path}")
                    return False
            else:
                print(f"ℹ️ [DEBUG] 没有找到历史交易数据文件 | 文件: {file_path}")
                return False
                
        except Exception as e:
            ERROR_LOGGER.error(f"读取持仓数据失败 | 合约: {symbol} | 错误: {e}")
            print(f"❌ [DEBUG] 读取持仓数据失败 | 合约: {symbol} | 错误: {e}")
            return False
    
    def save_position_to_csv(self, symbol):
        """保存当前持仓和交易数据到CSV - 参照sp.py实现"""
        try:
            import pandas as pd
            import os
            import re
            from datetime import datetime
            
            # 使用正则表达式提取英文字母作为符号标识
            clean_symbol = ''.join(re.findall('[a-zA-Z]', str(symbol)))
            
            # 获取当前持仓状态
            current_pos = self.current_positions.get(symbol, 0)
            
            # 获取止损止盈信息
            long_info = self.stop_order_dict.get(symbol, {}).get('long', {})
            short_info = self.stop_order_dict.get(symbol, {}).get('short', {})
            
            # 创建DataFrame数据
            data = {
                'datetime': [datetime.now().strftime('%Y-%m-%d %H:%M:%S')],
                'symbol': [symbol],
                'pos': [current_pos],
                'long_entry_price': [long_info.get('entry_price', 0)],
                'short_entry_price': [short_info.get('entry_price', 0)],
                'long_stop_loss': [long_info.get('stop_loss', 0)],
                'short_stop_loss': [short_info.get('stop_loss', 0)],
                'long_take_profit': [long_info.get('take_profit', 0)],
                'short_take_profit': [short_info.get('take_profit', 0)],
                'long_trailing_stop': [long_info.get('trailing_stop', 0)],
                'short_trailing_stop': [short_info.get('trailing_stop', 0)]
            }
            
            df = pd.DataFrame(data)
            
            # 确保目录存在
            os.makedirs("traderdata", exist_ok=True)
            
            # 保存到CSV文件
            csv_path = f"traderdata/{clean_symbol}traderdata.csv"
            df.to_csv(csv_path, index=False)
            
            print(f"✅ [DEBUG] 持仓信息已保存 | 合约: {symbol} | 持仓: {current_pos} | 文件: {csv_path}")
            POSITION_LOGGER.info(f"持仓信息已保存 | 合约: {symbol} | 持仓: {current_pos}")
            
        except Exception as e:
            ERROR_LOGGER.error(f"保存持仓信息失败 | 合约: {symbol} | 错误: {e}")
            print(f"❌ [DEBUG] 保存持仓信息失败 | 合约: {symbol} | 错误: {e}")
    
    def save_trade_record_to_csv(self, symbol, trade_info):
        """保存成交记录到CSV文件"""
        try:
            import pandas as pd
            import os
            from datetime import datetime
            
            # 成交记录文件路径
            trade_records_path = f"traderdata/{symbol}_trade_records.csv"
            
            # 准备成交记录数据
            trade_data = {
                'datetime': [datetime.now().strftime('%Y-%m-%d %H:%M:%S')],
                'symbol': [symbol],
                'direction': [trade_info['direction']],  # 买入/卖出
                'offset': [trade_info['offset']],  # 开仓/平仓
                'price': [trade_info['price']],
                'volume': [trade_info['volume']],
                'trade_id': [trade_info.get('trade_id', '')],
                'order_ref': [trade_info.get('order_ref', '')],
                'position_after': [self.current_positions.get(symbol, 0)]
            }
            
            df_new = pd.DataFrame(trade_data)
            
            # 如果文件存在，追加数据；否则创建新文件
            if os.path.exists(trade_records_path):
                df_new.to_csv(trade_records_path, mode='a', header=False, index=False)
            else:
                os.makedirs("traderdata", exist_ok=True)
                df_new.to_csv(trade_records_path, index=False)
            
            print(f"📝 [DEBUG] 成交记录已保存 | 合约: {symbol} | 方向: {trade_info['direction']} | 价格: {trade_info['price']} | 数量: {trade_info['volume']}")
            ORDER_LOGGER.info(f"成交记录已保存 | 合约: {symbol} | {trade_info['direction']} {trade_info['offset']} | 价格: {trade_info['price']} | 数量: {trade_info['volume']}")
            
        except Exception as e:
            ERROR_LOGGER.error(f"保存成交记录失败 | 合约: {symbol} | 错误: {e}")
    
    def initialize_position_on_startup(self):
        """程序启动时初始化所有合约的持仓状态"""
        try:
            print("🔄 [DEBUG] 开始初始化持仓状态...")
            
            # 为所有交易合约读取历史持仓状态
            for symbol in self.symbols:
                if symbol:
                    self.read_position_from_csv(symbol)
            
            print("✅ [DEBUG] 持仓状态初始化完成")
            POSITION_LOGGER.info("程序启动持仓状态初始化完成")
            
        except Exception as e:
            ERROR_LOGGER.error(f"初始化持仓状态失败: {e}")
            print(f"❌ [DEBUG] 初始化持仓状态失败: {e}")
    
    def safe_decode_ctp_error(self, error_data, default=''):
        """安全解码CTP错误信息，处理字节串和字符串类型"""
        try:
            if not error_data:
                return default
            if isinstance(error_data, bytes):
                return error_data.decode('gbk')
            else:
                return str(error_data)
        except Exception as e:
            ERROR_LOGGER.warning(f"解码CTP错误信息失败: {e}")
            return default or '解码失败'
    
    def OnRtnOrder(self, pOrder):
        print("||订单回报||", pOrder)
        
    
    def OnRspOrderInsert(self, pInputOrder, pRspInfo, nRequestID, bIsLast):
        """报单录入响应"""
        print("||OnRspOrderInsert||", pInputOrder, pRspInfo, nRequestID, bIsLast)
        
        if pRspInfo:
            error_id = pRspInfo.get('ErrorID', 0)
            error_msg = self.safe_decode_ctp_error(pRspInfo.get('ErrorMsg', ''))
            
            if error_id != 0:
                ORDER_LOGGER.error(f"❌ 报单录入失败 | 错误代码: {error_id} | 错误信息: {error_msg}")
                print(f"❌ [DEBUG] 报单录入失败: {error_id} - {error_msg}")
                
                if pInputOrder:
                    instrument_id = pInputOrder.get('InstrumentID', '').decode() if pInputOrder.get('InstrumentID') else 'N/A'
                    direction = pInputOrder.get('Direction', '')
                    volume = pInputOrder.get('VolumeTotalOriginal', 0)
                    price = pInputOrder.get('LimitPrice', 0)
                    
                    ORDER_LOGGER.error(f"失败订单详情 | 合约: {instrument_id} | 方向: {direction} | 数量: {volume} | 价格: {price}")
                    print(f"❌ [DEBUG] 失败订单 - 合约: {instrument_id}, 方向: {direction}, 数量: {volume}, 价格: {price}")
            else:
                ORDER_LOGGER.info(f"✅ 报单录入成功 | 请求ID: {nRequestID}")
                print(f"✅ [DEBUG] 报单录入成功: {nRequestID}")
        else:
            ORDER_LOGGER.warning("报单录入响应信息为空")
            print("⚠️ [DEBUG] 报单录入响应信息为空")
    
    def OnRspQryTradingAccount(self, pTradingAccount, pRspInfo, nRequestID, bIsLast):
        """查询资金账户响应"""
        try:
            if pRspInfo and pRspInfo['ErrorID'] == 0:
                if pTradingAccount:
                    balance = float(pTradingAccount.get('Balance', 0))
                    available = float(pTradingAccount.get('Available', 0))
                    
                    # 直接更新账户信息，不依赖风险管理器
                    self.account_balance = balance
                    self.available_margin = available
                    SYSTEM_LOGGER.info(f"✅ 账户信息查询成功 | 总资金: {balance:.2f} | 可用资金: {available:.2f}")
                    SYSTEM_LOGGER.info(f"📊 账户状态更新 | 余额: {self.account_balance:.2f} | 保证金: {self.available_margin:.2f}")
                        
                    # 记录详细的账户信息
                    RISK_LOGGER.info(
                        f"账户详情 | "
                        f"总资金: {balance:.2f} | "
                        f"可用资金: {available:.2f} | "
                        f"保证金: {float(pTradingAccount.get('CurrMargin', 0)):.2f} | "
                        f"冻结: {float(pTradingAccount.get('FrozenMargin', 0)):.2f} | "
                        f"手续费: {float(pTradingAccount.get('Commission', 0)):.2f} | "
                        f"平仓盈亏: {float(pTradingAccount.get('CloseProfit', 0)):.2f}"
                    )
                else:
                    SYSTEM_LOGGER.warning("账户查询响应为空")
            else:
                # 使用安全解码函数处理错误信息
                if pRspInfo:
                    error_msg = self.safe_decode_ctp_error(pRspInfo.get('ErrorMsg', ''), '未知错误')
                    error_id = pRspInfo.get('ErrorID', -1)
                else:
                    error_msg = "未知错误"
                    error_id = -1
                ERROR_LOGGER.error(f"查询账户信息失败 | 错误ID: {error_id} | 错误: {error_msg}")
                
        except Exception as e:
            ERROR_LOGGER.error(f"处理账户查询响应失败: {e}")
    
    def req_qry_trading_account(self):
        """查询资金账户"""
        try:
            # 简化版本：直接使用最基本的参数
            result = self.ReqQryTradingAccount({}, 1)
            
            if result != 0:
                ERROR_LOGGER.warning(f"账户查询请求失败，返回值: {result}，继续运行")
            else:
                SYSTEM_LOGGER.info("账户查询请求发送成功")
        except Exception as e:
            # 账户查询失败不影响交易，只记录警告
            ERROR_LOGGER.warning(f"账户查询异常: {e}，跳过账户查询继续运行")
            SYSTEM_LOGGER.info("系统将继续运行，不影响交易功能")
    
    
    def update_stop_order_dict(self, instrument_id, direction, position, entry_price=None, stop_loss=None, take_profit=None, trailing_stop=None):
        """更新止损止盈信息 """
        if instrument_id not in self.stop_order_dict:
            self.stop_order_dict[instrument_id] = {
                'long': {'position': 0, 'entry_price': 0, 'stop_loss': 0, 'take_profit': 0, 'trailing_stop': 0},
                'short': {'position': 0, 'entry_price': 0, 'stop_loss': 0, 'take_profit': 0, 'trailing_stop': 0}
            }
        
        if position is not None:
            self.stop_order_dict[instrument_id][direction]['position'] = position
        if entry_price is not None:
            self.stop_order_dict[instrument_id][direction]['entry_price'] = entry_price
        if stop_loss is not None:
            self.stop_order_dict[instrument_id][direction]['stop_loss'] = stop_loss
        if take_profit is not None:
            self.stop_order_dict[instrument_id][direction]['take_profit'] = take_profit
        if trailing_stop is not None:
            self.stop_order_dict[instrument_id][direction]['trailing_stop'] = trailing_stop
    
    def clear_position_info(self, instrument_id, direction):
        """清空指定合约和方向的持仓信息 """
        if instrument_id not in self.stop_order_dict:
            return
            
        if direction == 'long' or direction == 'all':
            self.stop_order_dict[instrument_id]['long'] = {
                'position': 0, 'entry_price': 0, 'stop_loss': 0, 
                'take_profit': 0, 'trailing_stop': 0
            }
            
        if direction == 'short' or direction == 'all':
            self.stop_order_dict[instrument_id]['short'] = {
                'position': 0, 'entry_price': 0, 'stop_loss': 0, 
                'take_profit': 0, 'trailing_stop': 0
            }
        
        if direction == 'all':
            self.current_positions[instrument_id] = 0
        elif direction == 'long':
            if self.current_positions.get(instrument_id, 0) > 0:
                self.current_positions[instrument_id] = 0
        elif direction == 'short':
            if self.current_positions.get(instrument_id, 0) < 0:
                self.current_positions[instrument_id] = 0
                
        SIGNAL_LOGGER.info(f"已清空{instrument_id}的{direction}持仓信息")
    
    
    
    
    
    
    
    
    
    def update_bar_data(self, symbol, tick_data):
        """更新K线数据"""
        try:
            if symbol not in self.bar_data_cache:
                self.bar_data_cache[symbol] = []
            
            # 简化的K线生成逻辑
            current_time = tick_data['datetime']
            price = tick_data['price']
            volume = tick_data['volume']
            
            new_bar_created = False
            
            # 如果是新的时间周期，创建新的K线
            if not self.bar_data_cache[symbol] or self.should_create_new_bar(symbol, current_time):
                new_bar = {
                    'symbol': symbol,
                    'datetime': current_time,
                    'open': price,
                    'high': price,
                    'low': price,
                    'close': price,
                    'volume': volume
                }
                self.bar_data_cache[symbol].append(new_bar)
                new_bar_created = True
                SIGNAL_LOGGER.info(f"{symbol} 新K线创建: 时间={current_time}, 价格={price}")
            else:
                # 更新当前K线
                current_bar = self.bar_data_cache[symbol][-1]
                current_bar['high'] = max(current_bar['high'], price)
                current_bar['low'] = min(current_bar['low'], price)
                current_bar['close'] = price
                current_bar['volume'] += volume
            
            # 保持缓存大小
            if len(self.bar_data_cache[symbol]) > 200:
                self.bar_data_cache[symbol] = self.bar_data_cache[symbol][-200:]
            
            # 如果创建了新K线且有足够数据，立即检查交易信号
            if new_bar_created and len(self.bar_data_cache[symbol]) >= self.config.min_bar_count:
                SIGNAL_LOGGER.info(f"{symbol} 新K线触发交易信号检查，当前K线数量: {len(self.bar_data_cache[symbol])}")
                self.check_trading_signals(symbol)
                
        except Exception as e:
            SIGNAL_LOGGER.error(f"更新K线数据失败: {e}")
    
    def should_create_new_bar(self, symbol, current_time):
        """判断是否应该创建新的K线"""
        if not self.bar_data_cache[symbol]:
            return True
        
        last_bar_time = self.bar_data_cache[symbol][-1]['datetime']
        time_diff = (current_time - last_bar_time).total_seconds()
        
        # 5分钟K线
        return time_diff >= 300
    
    
    
    
    
    
    
    def get_exchange_id(self, symbol):
        """根据合约代码获取交易所ID"""
        try:
            # 提取品种代码
            import re
            match = re.match(r'^([a-zA-Z]+)', symbol)
            if not match:
                return 'SHFE'  # 默认上期所
                
            variety = match.group(1).upper()
            
            # 交易所映射
            exchange_map = {
                'AU': 'SHFE',    # 黄金
                'AG': 'SHFE',    # 白银
                'CU': 'SHFE',    # 铜
                'AL': 'SHFE',    # 铝
                'ZN': 'SHFE',    # 锌
                'PB': 'SHFE',    # 铅
                'NI': 'SHFE',    # 镍
                'SN': 'SHFE',    # 锡
                'RB': 'SHFE',    # 螺纹钢
                'HC': 'SHFE',    # 热卷
                'WR': 'SHFE',    # 线材
                'FU': 'SHFE',    # 燃料油
                'BU': 'SHFE',    # 沥青
                'RU': 'SHFE',    # 橡胶
                'A': 'DCE',      # 豆一
                'B': 'DCE',      # 豆二
                'M': 'DCE',      # 豆粕
                'Y': 'DCE',      # 豆油
                'C': 'DCE',      # 玉米
                'CS': 'DCE',     # 玉米淀粉
                'I': 'DCE',      # 铁矿石
                'J': 'DCE',      # 焦炭
                'JM': 'DCE',     # 焦煤
                'L': 'DCE',      # 塑料
                'V': 'DCE',      # PVC
                'PP': 'DCE',     # PP
                'EG': 'DCE',     # 乙二醇
                'EB': 'DCE',     # 苯乙烯
                'P': 'DCE',      # 棕榈油
                'CF': 'CZCE',    # 棉花
                'CY': 'CZCE',    # 棉纱
                'SR': 'CZCE',    # 白糖
                'TA': 'CZCE',    # PTA
                'OI': 'CZCE',    # 菜油
                'MA': 'CZCE',    # 甲醇
                'FG': 'CZCE',    # 玻璃
                'RM': 'CZCE',    # 菜粕
                'ZC': 'CZCE',    # 动力煤
                'SF': 'CZCE',    # 硅铁
                'SM': 'CZCE',    # 锰硅
                'AP': 'CZCE',    # 苹果
                'CJ': 'CZCE',    # 红枣
                'UR': 'CZCE',    # 尿素
                'SA': 'CZCE',    # 纯碱
                'PF': 'CZCE',    # 短纤
                'PK': 'CZCE',    # 花生
                'IF': 'CFFEX',   # 沪深300
                'IC': 'CFFEX',   # 中证500
                'IH': 'CFFEX',   # 上证50
                'IM': 'CFFEX',   # 中证1000
                'T': 'CFFEX',    # 10年国债
                'TF': 'CFFEX',   # 5年国债
                'TS': 'CFFEX',   # 2年国债
            }
            
            return exchange_map.get(variety, 'SHFE')
            
        except Exception as e:
            SIGNAL_LOGGER.error(f"获取交易所ID失败: {e}")
            return 'SHFE'
    
    
    
    def load_historical_data(self, instrument_id):
        """加载历史数据以满足特征计算需求"""
        try:
            DATA_LOGGER.info(f"开始加载历史数据 | 合约: {instrument_id}")
            
            # 获取模型对应的合约代码
            model_symbol = self.model_manager.get_model_symbol(instrument_id) if hasattr(self, 'model_manager') else instrument_id
            print(f"🔄 [DEBUG] 历史数据合约映射 | 交易合约: {instrument_id} | 数据合约: {model_symbol}")
            
            # 计算需要的最小历史数据量（确保能计算所有特征）
            min_required = max(100, self.config.min_bar_count * 5)  # 至少100根K线，确保60窗口特征可用
            print(f"📊 [DEBUG] 需要历史数据量: {min_required} 根K线")
            
            historical_bars = []
            
            # 1. 优先从CSV文件加载历史数据（这是主要数据源）
            csv_patterns = [
                f"data/{model_symbol}*.csv",  # 优先加载训练合约的数据（如au888）
                f"data/{instrument_id}*.csv",
                f"{model_symbol}*.csv", 
                f"{instrument_id}*.csv"
            ]
            
            # 兼容回测系统的文件命名规范
            # 回测系统的文件名格式: {symbol}_{start_date}_{end_date}_{period}_adj{type}.csv
            # 但实际文件可能缺少 _adj0 后缀，所以需要兼容多种格式
            additional_patterns = [
                f"data/{model_symbol}_*_*_*m_adj*.csv",    # 完整回测格式
                f"data/{model_symbol}_*_*_*m.csv",         # 无adj后缀格式  
                f"data/{model_symbol}_*_*_*.csv",          # 通用格式
            ]
            csv_patterns.extend(additional_patterns)
            
            import glob
            csv_loaded = False
            for pattern in csv_patterns:
                csv_files = glob.glob(pattern)
                if csv_files:
                    csv_file_path = csv_files[0]  # 使用第一个找到的文件
                    try:
                        print(f"📊 [DEBUG] 尝试从CSV文件加载 | 文件: {csv_file_path}")
                        csv_df = pd.read_csv(csv_file_path)
                        
                        if len(csv_df) > 0:
                            print(f"📊 [DEBUG] CSV文件原始数据量: {len(csv_df)}")
                            
                            # 标准化列名映射
                            column_mapping = {
                                'timestamp': 'datetime',
                                'time': 'datetime', 
                                'date': 'datetime',
                                'DateTime': 'datetime',
                                'o': 'open', 'Open': 'open',
                                'h': 'high', 'High': 'high', 
                                'l': 'low', 'Low': 'low',
                                'c': 'close', 'Close': 'close',
                                'v': 'volume', 'Volume': 'volume', 'vol': 'volume'
                            }
                            
                            # 应用列名映射
                            original_columns = list(csv_df.columns)
                            csv_df = csv_df.rename(columns=column_mapping)
                            print(f"📊 [DEBUG] 列名映射 | 原始: {original_columns[:5]} | 映射后: {list(csv_df.columns)[:5]}")
                            
                            # 检查必要的列是否存在
                            required_cols = ['datetime', 'open', 'high', 'low', 'close', 'volume']
                            missing_cols = [col for col in required_cols if col not in csv_df.columns]
                            
                            if missing_cols:
                                print(f"❌ [DEBUG] CSV文件缺少必要列 | 文件: {csv_file_path} | 缺少: {missing_cols}")
                                continue
                            
                            # 时间格式处理
                            try:
                                csv_df['datetime'] = pd.to_datetime(csv_df['datetime'])
                                print(f"✅ [DEBUG] 时间格式转换成功")
                            except Exception as e:
                                print(f"❌ [DEBUG] 时间格式转换失败: {e}")
                                continue
                            
                            # 数据类型转换和清洗
                            try:
                                for col in ['open', 'high', 'low', 'close']:
                                    csv_df[col] = pd.to_numeric(csv_df[col], errors='coerce')
                                csv_df['volume'] = pd.to_numeric(csv_df['volume'], errors='coerce').fillna(0).astype(int)
                                
                                # 删除含有NaN的行
                                before_clean = len(csv_df)
                                csv_df = csv_df.dropna(subset=required_cols[:5])  # 不包括volume
                                after_clean = len(csv_df)
                                print(f"📊 [DEBUG] 数据清洗 | 清洗前: {before_clean} | 清洗后: {after_clean}")
                                
                            except Exception as e:
                                print(f"❌ [DEBUG] 数据类型转换失败: {e}")
                                continue
                            
                            # 按时间排序并取最新数据
                            csv_df = csv_df.sort_values('datetime')
                            if len(csv_df) > min_required:
                                csv_data = csv_df.tail(min_required)
                            else:
                                csv_data = csv_df
                            
                            print(f"📊 [DEBUG] 选择数据 | 可用总量: {len(csv_df)} | 选择数量: {len(csv_data)}")
                            
                            # 转换为历史数据格式
                            for _, row in csv_data.iterrows():
                                bar_dict = {
                                    'datetime': row['datetime'],
                                    'symbol': model_symbol,  # 使用模型对应的合约代码
                                    'open': float(row['open']),
                                    'high': float(row['high']), 
                                    'low': float(row['low']),
                                    'close': float(row['close']),
                                    'volume': int(row['volume'])
                                }
                                historical_bars.append(bar_dict)
                            
                            print(f"✅ [DEBUG] CSV历史数据加载成功 | 文件: {csv_file_path} | 加载数量: {len(csv_data)}")
                            DATA_LOGGER.info(f"从CSV文件加载历史数据成功 | 文件: {csv_file_path} | 数量: {len(csv_data)}")
                            csv_loaded = True
                            break
                        
                    except Exception as e:
                        print(f"❌ [DEBUG] CSV文件加载失败 | 文件: {csv_file_path} | 错误: {e}")
                        DATA_LOGGER.warning(f"从CSV文件加载数据失败 | 文件: {csv_file_path} | 错误: {e}")
                        continue
            
            # 2. 如果CSV加载失败，尝试从JSON文件加载（备用方案）
            if not csv_loaded or len(historical_bars) < min_required:
                print(f"🔄 [DEBUG] CSV加载不足，尝试JSON文件补充 | 当前数量: {len(historical_bars)} | 需要: {min_required}")
                
                json_patterns = [
                    f"traderdata/{model_symbol}_ofdata.json",
                    f"traderdata/{instrument_id}_ofdata.json"
                ]
                
                for json_file_path in json_patterns:
                    if os.path.exists(json_file_path):
                        try:
                            print(f"📊 [DEBUG] 尝试从JSON文件加载 | 文件: {json_file_path}")
                            historical_df = pd.read_json(json_file_path, lines=True)
                            
                            if len(historical_df) > 0:
                                print(f"📊 [DEBUG] JSON文件数据量: {len(historical_df)}")
                                
                                # 确保datetime字段格式正确
                                if 'datetime' in historical_df.columns:
                                    historical_df['datetime'] = pd.to_datetime(historical_df['datetime'])
                                
                                # 按时间排序并取最新数据
                                historical_df = historical_df.sort_values('datetime')
                                needed = min_required - len(historical_bars)
                                supplement_data = historical_df.tail(needed) if needed > 0 else pd.DataFrame()
                                
                                # 转换为缓存格式并存储
                                for _, row in supplement_data.iterrows():
                                    bar_dict = {
                                        'datetime': row['datetime'],
                                        'symbol': model_symbol,
                                        'open': float(row.get('open', row.get('close', 0))),
                                        'high': float(row.get('high', row.get('close', 0))),
                                        'low': float(row.get('low', row.get('close', 0))),
                                        'close': float(row.get('close', 0)),
                                        'volume': int(row.get('volume', 0))
                                    }
                                    historical_bars.append(bar_dict)
                                
                                print(f"✅ [DEBUG] JSON补充数据成功 | 补充数量: {len(supplement_data)} | 总数量: {len(historical_bars)}")
                                DATA_LOGGER.info(f"从JSON文件补充历史数据 | 文件: {json_file_path} | 补充数量: {len(supplement_data)}")
                                
                                if len(historical_bars) >= min_required:
                                    break
                                    
                        except Exception as e:
                            print(f"❌ [DEBUG] JSON文件加载失败 | 文件: {json_file_path} | 错误: {e}")
                            DATA_LOGGER.warning(f"从JSON文件加载数据失败: {e}")
            
            # 3. 将历史数据存储到缓存中
            if historical_bars:
                if instrument_id not in self.bar_data_cache:
                    self.bar_data_cache[instrument_id] = []
                
                # 合并历史数据，避免重复
                existing_times = set()
                if self.bar_data_cache[instrument_id]:
                    existing_times = {bar['datetime'] for bar in self.bar_data_cache[instrument_id]}
                
                # 添加新的历史数据
                added_count = 0
                for bar in historical_bars:
                    bar_datetime = bar['datetime']
                    # 确保datetime是字符串格式，便于比较
                    if hasattr(bar_datetime, 'strftime'):
                        bar_datetime = bar_datetime.strftime('%Y-%m-%d %H:%M:%S')
                        bar['datetime'] = bar_datetime
                    
                    if bar_datetime not in existing_times:
                        self.bar_data_cache[instrument_id].append(bar)
                        existing_times.add(bar_datetime)
                        added_count += 1
                
                print(f"📊 [DEBUG] 历史数据添加到缓存 | 新增: {added_count} | 缓存总量: {len(self.bar_data_cache[instrument_id])}")
                
                # 按时间排序并保持足够的历史数据用于特征计算
                self.bar_data_cache[instrument_id].sort(key=lambda x: x['datetime'])
                
                # 保留更多历史数据以支持大窗口特征计算
                max_cache_size = max(200, self.config.min_bar_count * 10)  # 至少200根K线
                if len(self.bar_data_cache[instrument_id]) > max_cache_size:
                    self.bar_data_cache[instrument_id] = self.bar_data_cache[instrument_id][-max_cache_size:]
                
                print(f"✅ [DEBUG] 历史数据缓存完成 | 合约: {instrument_id} | 最终缓存数量: {len(self.bar_data_cache[instrument_id])}")
                DATA_LOGGER.info(f"历史数据缓存更新 | 合约: {instrument_id} | 缓存数量: {len(self.bar_data_cache[instrument_id])}")
                
                # 3.5. 重要：基于历史数据初始化CSV文件
                try:
                    self.initialize_csv_with_historical_data(instrument_id)
                    print(f"✅ [DEBUG] CSV文件初始化完成 | 合约: {instrument_id}")
                except Exception as csv_error:
                    ERROR_LOGGER.error(f"CSV文件初始化失败 | 合约: {instrument_id} | 错误: {csv_error}")
                
                # 4. 重要：将历史数据也加载到全局trader_df中，以便ML信号处理
                self.initialize_trader_df_with_history(instrument_id, historical_bars)
                
                # 5. 验证数据质量
                if len(historical_bars) >= min_required:
                    print(f"✅ [DEBUG] 历史数据加载成功 | 合约: {instrument_id} | 加载数量: {len(historical_bars)} >= 需要数量: {min_required}")
                    return True
                else:
                    print(f"⚠️ [DEBUG] 历史数据不足 | 合约: {instrument_id} | 加载数量: {len(historical_bars)} < 需要数量: {min_required}")
                    DATA_LOGGER.warning(f"历史数据数量不足 | 合约: {instrument_id} | 加载: {len(historical_bars)} | 需要: {min_required}")
                    return True  # 仍然返回True，允许继续运行，等待实时数据积累
                    
            else:
                # 确保缓存初始化
                if instrument_id not in self.bar_data_cache:
                    self.bar_data_cache[instrument_id] = []
                
                print(f"❌ [DEBUG] 未找到任何历史数据文件 | 合约: {instrument_id}")
                DATA_LOGGER.warning(f"未找到历史数据文件 | 合约: {instrument_id} | 将等待实时数据积累")
                return False
            
        except Exception as e:
            print(f"❌ [DEBUG] 加载历史数据异常 | 合约: {instrument_id} | 错误: {e}")
            ERROR_LOGGER.error(f"加载历史数据异常 | 合约: {instrument_id} | 错误: {e}")
            import traceback
            print(f"🔍 [DEBUG] 异常堆栈: {traceback.format_exc()}")
            
            # 确保缓存初始化
            if instrument_id not in self.bar_data_cache:
                self.bar_data_cache[instrument_id] = []
            return False
    
    def initialize_trader_df_with_history(self, instrument_id, historical_bars):
        """使用历史数据初始化trader_df"""
        try:
            global trader_df
            
            # 转换历史数据为trader_df格式
            history_data = []
            for bar in historical_bars:
                history_data.append({
                    'datetime': bar['datetime'],
                    'open': bar['open'],
                    'high': bar['high'],
                    'low': bar['low'],
                    'close': bar['close'],
                    'volume': bar['volume'],
                    'delta': 0,  # 历史数据没有delta信息，设为0
                    'dj': 0     # 历史数据没有dj信息，设为0
                })
            
            if history_data:
                history_df = pd.DataFrame(history_data)
                
                # 如果trader_df为空或长度不足，用历史数据初始化
                if trader_df.empty or len(trader_df) < self.config.min_bar_count:
                    # 只保留最新的n根K线作为初始数据
                    init_data = history_df.tail(self.config.min_bar_count)
                    trader_df = init_data.copy()
                    
                    # 确保trader_df有必要的列
                    required_cols = ['delta', 'dj']
                    for col in required_cols:
                        if col not in trader_df.columns:
                            trader_df[col] = 0
                    
                    DATA_LOGGER.info(f"trader_df初始化完成 | 合约: {instrument_id} | 历史K线数: {len(trader_df)}")
                else:
                    # 如果trader_df已有数据，检查是否需要补充历史数据
                    if len(trader_df) < self.config.min_bar_count:
                        # 计算需要补充的数据量
                        needed = self.config.min_bar_count - len(trader_df)
                        supplement_data = history_df.tail(needed)
                        
                        # 按时间合并，避免重复
                        trader_df['datetime'] = pd.to_datetime(trader_df['datetime'])
                        supplement_data['datetime'] = pd.to_datetime(supplement_data['datetime'])
                        
                        # 确保trader_df有必要的列
                        required_cols = ['delta', 'dj']
                        for col in required_cols:
                            if col not in trader_df.columns:
                                trader_df[col] = 0
                        
                        # 找出不重复的历史数据
                        existing_times = set(trader_df['datetime'])
                        new_data = supplement_data[~supplement_data['datetime'].isin(existing_times)]
                        
                        if len(new_data) > 0:
                            trader_df = pd.concat([new_data, trader_df], ignore_index=True)
                            trader_df = trader_df.sort_values('datetime').reset_index(drop=True)
                            
                            DATA_LOGGER.info(f"trader_df补充历史数据 | 合约: {instrument_id} | 补充: {len(new_data)} | 总计: {len(trader_df)}")
                
                return True
                
        except Exception as e:
            ERROR_LOGGER.error(f"初始化trader_df失败 | 合约: {instrument_id} | 错误: {e}")
            return False
    
    def check_stop_conditions(self, data):
        """检查是否满足止盈止损条件 """
        instrument_id = data['InstrumentID'].decode()
        
        # 如果该合约不在止盈止损字典中，直接返回
        if instrument_id not in self.stop_order_dict:
            return
        
        current_bid = float(data['BidPrice1'])  # 当前买价
        current_ask = float(data['AskPrice1'])  # 当前卖价
        
        stops = self.stop_order_dict[instrument_id]
        
        # 检查多头止盈止损
        if stops['long']['position'] > 0:
            entry_price = stops['long']['entry_price']  # 获取开仓价
            
            # 检查止损
            if stops['long']['stop_loss'] > 0 and current_bid <= stops['long']['stop_loss']:
                STOPLOSS_LOGGER.info(f"触发多头止损: {instrument_id}, 价格: {current_bid}, 止损价: {stops['long']['stop_loss']}")
                self.insert_order(data['ExchangeID'], data['InstrumentID'], current_bid-self.py, 
                                stops['long']['position'], b'1', b'3')
                # 清空多头持仓信息
                self.clear_position_info(instrument_id, 'long')
                self.current_positions[instrument_id] = 0
            
            # 检查跟踪止损 - 新增开仓价判断条件
            elif stops['long']['trailing_stop'] > 0 and current_bid < stops['long']['trailing_stop'] and current_bid > entry_price:
                STOPLOSS_LOGGER.info(f"触发多头跟踪止损: {instrument_id}, 价格: {current_bid}, 跟踪止损价: {stops['long']['trailing_stop']}, 开仓价: {entry_price}")
                self.insert_order(data['ExchangeID'], data['InstrumentID'], current_bid-self.py, 
                                stops['long']['position'], b'1', b'3')
                # 清空多头持仓信息
                self.clear_position_info(instrument_id, 'long')
                self.current_positions[instrument_id] = 0
                
            # 检查止盈
            elif stops['long']['take_profit'] > 0 and current_bid >= stops['long']['take_profit']:
                STOPLOSS_LOGGER.info(f"触发多头止盈: {instrument_id}, 价格: {current_bid}, 止盈价: {stops['long']['take_profit']}")
                self.insert_order(data['ExchangeID'], data['InstrumentID'], current_bid-self.py, 
                                stops['long']['position'], b'1', b'3')
                # 清空多头持仓信息
                self.clear_position_info(instrument_id, 'long')
                self.current_positions[instrument_id] = 0
            
            # 更新跟踪止损价 - 只在价格上涨且大于开仓价时更新
            elif stops['long']['trailing_stop'] > 0 and current_bid > entry_price:
                # 只有当前价格比之前设置的跟踪止损价高一定幅度时才更新
                new_trailing_stop = current_bid * (1 - self.config.trailing_stop_distance)
                if new_trailing_stop > stops['long']['trailing_stop']:
                    self.update_stop_order_dict(instrument_id, 'long', None, None, None, None, new_trailing_stop)
                    STOPLOSS_LOGGER.debug(f"更新多头跟踪止损: {instrument_id}, 新止损价: {new_trailing_stop}, 开仓价: {entry_price}")
        
        # 检查空头止盈止损
        if stops['short']['position'] > 0:
            entry_price = stops['short']['entry_price']  # 获取开仓价
            
            # 检查止损
            if stops['short']['stop_loss'] > 0 and current_ask >= stops['short']['stop_loss']:
                STOPLOSS_LOGGER.info(f"触发空头止损: {instrument_id}, 价格: {current_ask}, 止损价: {stops['short']['stop_loss']}")
                self.insert_order(data['ExchangeID'], data['InstrumentID'], current_ask+self.py, 
                                stops['short']['position'], b'0', b'3')
                # 清空空头持仓信息
                self.clear_position_info(instrument_id, 'short')
                self.current_positions[instrument_id] = 0
            
            # 检查跟踪止损 - 新增开仓价判断条件
            elif stops['short']['trailing_stop'] > 0 and current_ask > stops['short']['trailing_stop'] and current_ask < entry_price:
                STOPLOSS_LOGGER.info(f"触发空头跟踪止损: {instrument_id}, 价格: {current_ask}, 跟踪止损价: {stops['short']['trailing_stop']}, 开仓价: {entry_price}")
                self.insert_order(data['ExchangeID'], data['InstrumentID'], current_ask+self.py, 
                                stops['short']['position'], b'0', b'3')
                # 清空空头持仓信息
                self.clear_position_info(instrument_id, 'short')
                self.current_positions[instrument_id] = 0
            
            # 检查止盈
            elif stops['short']['take_profit'] > 0 and current_ask <= stops['short']['take_profit']:
                STOPLOSS_LOGGER.info(f"触发空头止盈: {instrument_id}, 价格: {current_ask}, 止盈价: {stops['short']['take_profit']}")
                self.insert_order(data['ExchangeID'], data['InstrumentID'], current_ask+self.py, 
                                stops['short']['position'], b'0', b'3')
                # 清空空头持仓信息
                self.clear_position_info(instrument_id, 'short')
                self.current_positions[instrument_id] = 0
            
            # 更新跟踪止损价 - 只在价格下跌且小于开仓价时更新
            elif stops['short']['trailing_stop'] > 0 and current_ask < entry_price:
                # 只有当前价格比之前设置的跟踪止损价低一定幅度时才更新
                new_trailing_stop = current_ask * (1 + self.config.trailing_stop_distance)
                if new_trailing_stop < stops['short']['trailing_stop'] or stops['short']['trailing_stop'] == 0:
                    self.update_stop_order_dict(instrument_id, 'short', None, None, None, None, new_trailing_stop)
                    STOPLOSS_LOGGER.debug(f"更新空头跟踪止损: {instrument_id}, 新止损价: {new_trailing_stop}, 开仓价: {entry_price}")

    def generate_ml_trading_decision(self, symbol, prediction, current_price):
        """
        基于ML预测和回测逻辑直接执行交易
        完全按照ml_backtest_complete.py中的逻辑实现
        """
        try:
            # 获取配置的阈值（如果没有配置则使用默认值）
            open_threshold = getattr(self.config, 'open_threshold', 0.002)
            close_threshold = getattr(self.config, 'close_threshold', 0.001)
            
            print(f"🎯 [DEBUG] 生成交易决策 | 合约: {symbol} | 预测值: {prediction:.6f} | 开仓阈值: {open_threshold} | 平仓阈值: {close_threshold}")
            
            # 标准化信号值（与回测系统保持一致）
            signal_value = np.tanh(prediction)  # 使用tanh函数标准化到[-1,1]范围
            print(f"📊 [DEBUG] 标准化信号值: {signal_value:.6f}")
            
            # 获取当前持仓状态
            current_pos = self.current_positions.get(symbol, 0)
            print(f"📋 [DEBUG] 当前持仓: {current_pos}")
            
            # 直接执行交易逻辑 - 完全按照回测系统逻辑
            lots = self.config.lots
            
            # 获取原始的tick数据，用于insert_order参数（直接使用data参数）
            if hasattr(self, '_current_tick_data') and self._current_tick_data:
                data = self._current_tick_data  # 直接使用原始CTP数据
                exchange_id = data['ExchangeID']
                instrument_id = data['InstrumentID']
                print(f"🔧 [DEBUG] 使用原始CTP数据:")
                print(f"  - ExchangeID: {exchange_id}")
                print(f"  - InstrumentID: {instrument_id}")
                print(f"  - AskPrice1: {data.get('AskPrice1', 0)}")
                print(f"  - BidPrice1: {data.get('BidPrice1', 0)}")
            else:
                SIGNAL_LOGGER.error(f"无法获取tick数据，无法执行交易 | 合约: {symbol}")
                return {
                    'signal_value': signal_value,
                    'raw_prediction': prediction,
                    'reason': '无法获取tick数据',
                    'current_position': current_pos,
                    'open_threshold': open_threshold,
                    'close_threshold': close_threshold,
                    'timestamp': datetime.now()
                }
            
            
            # AlgoPlus TraderApiBase 自动管理连接状态，无需手动检查
            print(f"✅ [DEBUG] 系统连接: AlgoPlus自动管理，继续交易逻辑")
            
            if current_pos == 0:  # 当前无持仓
                if signal_value > open_threshold:  # 开多
                    reason = f'开多信号，信号值: {signal_value:.4f} > 开仓阈值: {open_threshold}'     
                    py = 30  # 黄金期货价格滑点
                    ask_price1 = float(data.get('AskPrice1', current_price))
                    order_price = ask_price1 + py                  
                    SIGNAL_LOGGER.info(f"📈  | 合约: {symbol} | AskPrice1: {ask_price1} | 滑点: {py} | 手数: {lots} | {reason}")                 
                    self._insert_order_safe(data['ExchangeID'], data['InstrumentID'], order_price, lots, b'0', b'0')
                    SIGNAL_LOGGER.info(f"✅ 开多仓下单完成| {symbol} | 价格={order_price:.2f} | 手数={lots}")                   
                elif signal_value < -open_threshold:  # 开空
                    reason = f'开空信号，信号值: {signal_value:.4f} < -开仓阈值: {-open_threshold}'
                    py = 30  # 黄金期货价格滑点
                    bid_price1 = float(data.get('BidPrice1', current_price))
                    order_price = bid_price1 - py
                    self._insert_order_safe(data['ExchangeID'], data['InstrumentID'], order_price, lots, b'1', b'0')    
                else:
                    reason = f'信号不足，信号值: {signal_value:.4f} 在阈值范围内'
                    print(f"⏸️ [DEBUG] {reason}")
                    
            elif current_pos > 0:  # 当前持多
                if signal_value < -close_threshold:  # 平多
                    reason = f'平多信号，信号值: {signal_value:.4f} < -平仓阈值: {-close_threshold}'
                    print(f"📉 [DEBUG] {reason}")
                   
                    
                   
                    py = 30
                    bid_price1 = float(data.get('BidPrice1', current_price))
                    order_price = bid_price1 - py
                    
                    print(f"  - data['BidPrice1']: {bid_price1}")
                    print(f"  - py滑点: -{py}")
                    print(f"  - 下单价格: {order_price}")
                    print(f"  - 手数: {abs(current_pos)}")
                    print(f"  - 方向: b'1' (卖出)")
                    print(f"  - 开平: b'3' (平今)")                  
                    self._insert_order_safe(data['ExchangeID'], data['InstrumentID'], order_price, abs(current_pos), b'1', b'3')                    
                else:
                    reason = f'持多等待，信号值: {signal_value:.4f} >= -平仓阈值: {-close_threshold}'
                    print(f"📈 [DEBUG] {reason}")
                    
            elif current_pos < 0:  # 当前持空
                if signal_value > close_threshold:  # 平空
                    reason = f'平空信号，信号值: {signal_value:.4f} > 平仓阈值: {close_threshold}'
                    print(f"📈 [DEBUG] {reason}")
                    
                  
                    py = 30
                    ask_price1 = float(data.get('AskPrice1', current_price))
                    order_price = ask_price1 + py
                    
                    print(f"  - data['AskPrice1']: {ask_price1}")
                    print(f"  - py滑点: +{py}")
                    print(f"  - 下单价格: {order_price}")
                    print(f"  - 手数: {abs(current_pos)}")
                    print(f"  - 方向: b'0' (买入)")
                    print(f"  - 开平: b'3' (平今)")
                    

                    self._insert_order_safe(data['ExchangeID'], data['InstrumentID'], order_price, abs(current_pos), b'0', b'3')
                    
                else:
                    reason = f'持空等待，信号值: {signal_value:.4f} <= 平仓阈值: {close_threshold}'
                    print(f"📉 [DEBUG] {reason}")
            
            # 构建决策结果（仅用于记录）
            decision = {
                'signal_value': signal_value,
                'raw_prediction': prediction,
                'reason': reason,
                'current_position': current_pos,
                'open_threshold': open_threshold,
                'close_threshold': close_threshold,
                'timestamp': datetime.now()
            }
            
            return decision
            
        except Exception as e:
            ERROR_LOGGER.error(f"生成ML交易决策失败 | 合约: {symbol} | 错误: {e}")
            import traceback
            ERROR_LOGGER.error(f"详细错误: {traceback.format_exc()}")
            
            # 返回默认的决策
            return {
                'signal_value': 0,
                'raw_prediction': prediction,
                'reason': f'决策生成失败: {e}',
                'current_position': self.current_positions.get(symbol, 0),
                'timestamp': datetime.now()
            }

    def process_ml_trading_signals(self, symbol, data_df ):
        """处理机器学习交易信号 - 基于回测系统的逻辑"""
        try:
            # === 添加调试输出 ===
            print(f"🔍 [DEBUG] 开始处理ML信号 | 合约: {symbol} | 数据量: {len(data_df)}")
            SIGNAL_LOGGER.info(f"🔍 [DEBUG] process_ml_trading_signals调用 | 合约: {symbol} | 数据量: {len(data_df)}")
            
            # 检查模型管理器是否存在
            print(f"🔍 [DEBUG] 检查模型管理器 | 合约: {symbol}")
            if not hasattr(self, 'model_manager') or self.model_manager is None:
                print(f"❌ [DEBUG] 模型管理器未初始化 | 合约: {symbol}")
                SIGNAL_LOGGER.warning(f"模型管理器未初始化 | 合约: {symbol}")
                return
            
            print(f"✅ [DEBUG] 模型管理器检查通过 | 合约: {symbol}")
            
            # 确保有足够的数据
            print(f"🔍 [DEBUG] 检查数据量 | 合约: {symbol} | 当前: {len(data_df)} | 需要: {self.config.min_bar_count}")
            if len(data_df) < self.config.min_bar_count:
                print(f"⏳ [DEBUG] 数据不足 | 合约: {symbol} | 当前: {len(data_df)} | 需要: {self.config.min_bar_count}")
                DATA_LOGGER.debug(f"数据不足 | 合约: {symbol} | 当前数据: {len(data_df)} | 需要: {self.config.min_bar_count}")
                return
            
            print(f"✅ [DEBUG] 数据量检查通过 | 合约: {symbol}")
            
            SIGNAL_LOGGER.info(f"开始处理ML交易信号 | 合约: {symbol} | 数据量: {len(data_df)}")
            
            # 转换数据格式
            print(f"📊 [DEBUG] 开始数据格式转换 | 合约: {symbol}")
            df = data_df.tail(self.config.lookback_window).copy()
            print(f"✅ [DEBUG] 数据格式转换完成 | 合约: {symbol} | 使用数据量: {len(df)}")
            
            # 应用复权处理
            print(f"⚙️ [DEBUG] 开始复权处理 | 合约: {symbol}")
            try:
                df = apply_adjustment_factors_for_realtime(df)
                print(f"✅ [DEBUG] 复权处理完成 | 合约: {symbol}")
            except Exception as e:
                print(f"❌ [DEBUG] 复权处理失败 | 合约: {symbol} | 错误: {e}")
                return
            
            # 计算特征
            print(f"🧮 [DEBUG] 开始特征计算 | 合约: {symbol} | 输入数据量: {len(df)}")
            start_time = time.time()
            try:
                # 获取模型需要的特征列表
                target_features = None
                model_symbol = self.model_manager.get_model_symbol(symbol) if hasattr(self, 'model_manager') else symbol
                if hasattr(self, 'model_manager') and model_symbol in self.model_manager.feature_names:
                    target_features = self.model_manager.feature_names[model_symbol]
                    print(f"🎯 [DEBUG] 使用模型目标特征 | 数量: {len(target_features)}")
                
                # 使用配置中的lookback_window参数，并传递目标特征
                features = calculate_features(df, symbol, lookback_window=len(df), target_features=target_features)
                feature_calc_time = time.time() - start_time
                print(f"⏱️ [DEBUG] 特征计算耗时: {feature_calc_time:.3f}s | 合约: {symbol}")
                
                if features is not None:
                    print(f"✅ [DEBUG] 特征计算成功 | 特征维度: {features.shape if hasattr(features, 'shape') else 'Unknown'}")
                else:
                    print(f"❌ [DEBUG] 特征计算返回None")
                    
            except Exception as e:
                print(f"❌ [DEBUG] 特征计算异常 | 合约: {symbol} | 错误: {e}")
                import traceback
                print(f"🔍 [DEBUG] 异常堆栈: {traceback.format_exc()}")
                return
            
            if features is None or len(features) == 0:
                print(f"❌ [DEBUG] 特征计算结果为空 | 合约: {symbol}")
                DATA_LOGGER.warning(f"特征计算失败 | 合约: {symbol}")
                return
            # 获取价格信息：使用真实市场价格而不是复权价格
            # 复权价格只用于模型计算，交易时必须使用真实价格
            adjusted_price = float(df.iloc[-1]['close'])  # 复权价格，仅用于信号计算参考
            high_price = float(df.iloc[-1]['high'])
            low_price = float(df.iloc[-1]['low'])
            
            # 从tick数据获取真实市场价格用于交易
            if hasattr(self, '_current_tick_data') and self._current_tick_data:
                real_market_price = float(self._current_tick_data.get('LastPrice', adjusted_price))
                ask_price = float(self._current_tick_data.get('AskPrice1', real_market_price))
                bid_price = float(self._current_tick_data.get('BidPrice1', real_market_price))
                print(f"💰 [DEBUG] 价格信息 | 合约: {symbol}")
                print(f"  复权价格: {adjusted_price:.2f} (用于模型计算)")
                print(f"  真实市价: {real_market_price:.2f} (用于交易)")
                print(f"  卖一价: {ask_price:.2f}")
                print(f"  买一价: {bid_price:.2f}")
                current_price = real_market_price  # 使用真实市场价格
            else:
                print(f"⚠️ [DEBUG] 无法获取真实市价，使用复权价格 | 合约: {symbol}")
                current_price = adjusted_price
            
            # 模型预测
            print(f"🔮 [DEBUG] 开始模型预测 | 合约: {symbol}")
            prediction = self.model_manager.predict(symbol, features)
            if prediction is None:
                print(f"❌ [DEBUG] 模型预测失败 | 合约: {symbol}")
                MODEL_LOGGER.warning(f"模型预测失败 | 合约: {symbol}")
                return
            
            print(f"✅ [DEBUG] 模型预测成功 | 合约: {symbol} | 预测值: {prediction:.6f}")
            
            # === 更新CSV文件：添加预测值到最新K线数据 ===
            try:
                # 获取最新K线数据
                latest_bar = df.iloc[-1].to_dict()
                bar_data = {
                    'datetime': latest_bar['datetime'],
                    'symbol': symbol,
                    'open': latest_bar['open'],
                    'high': latest_bar['high'],
                    'low': latest_bar['low'],
                    'close': latest_bar['close'],
                    'volume': latest_bar['volume']
                }
                
                # 将预测值和K线数据保存到CSV
                self.append_new_bar_to_csv(symbol, bar_data, prediction)
                print(f"📊 [DEBUG] CSV文件已更新 | 合约: {symbol} | 时间: {latest_bar['datetime']} | 预测值: {prediction:.6f}")
                
            except Exception as csv_error:
                ERROR_LOGGER.error(f"更新CSV文件失败 | 合约: {symbol} | 错误: {csv_error}")
            
            # 生成交易决策 - 使用新的决策函数
            print(f"🎯 [DEBUG] 开始生成交易决策 | 合约: {symbol}")
            decision = self.generate_ml_trading_decision(symbol, prediction, current_price)
            
            print(f"📋 [DEBUG] 交易决策完成 | 合约: {symbol} | 信号值: {decision.get('signal_value', 'N/A'):.4f}")
            
            # === 每5分钟输出详细的机器学习交易信号 ===
            print(f"📢 [DEBUG] 准备输出ML信号 | 合约: {symbol}")
            self.output_ml_trading_signals(symbol, df.iloc[-1]['datetime'], prediction, decision, current_price, high_price, low_price, features)
            print(f"✅ [DEBUG] ML信号输出完成 | 合约: {symbol}")
            
            # 记录模型性能指标
            if hasattr(self, 'last_predictions'):
                if symbol not in self.last_predictions:
                    self.last_predictions[symbol] = []
                self.last_predictions[symbol].append({
                    'time': datetime.now(),
                    'prediction': prediction,
                    'price': current_price,
                    'signal': decision.get('signal_value', 0)
                })
                # 只保留最近100个预测记录
                if len(self.last_predictions[symbol]) > 100:
                    self.last_predictions[symbol] = self.last_predictions[symbol][-100:]
            
            # === 动态学习：收集训练数据 ===
            if hasattr(self, 'enhanced_strategy') and self.enhanced_strategy and self.config.online_learning_enabled:
                try:
                    if hasattr(self.enhanced_strategy, 'collect_training_data'):
                        self.enhanced_strategy.collect_training_data(symbol, features, current_price)
                    else:
                        # 使用简化的数据收集方法
                        self.collect_training_data(symbol, features, current_price)
                except Exception as e:
                    LEARNING_LOGGER.warning(f"训练数据收集失败 | 合约: {symbol} | 错误: {e}")
                
        except Exception as e:
            ERROR_LOGGER.error(f"ML交易信号处理异常 | 合约: {symbol} | 错误: {e}")
            import traceback
            ERROR_LOGGER.error(f"详细错误信息: {traceback.format_exc()}")
    
    def output_ml_trading_signals(self, symbol, timestamp, prediction, decision, current_price, high_price, low_price, features):
        """输出详细的机器学习交易信号"""
        try:
            # === 立即调试输出 ===
            print(f"🚀 [DEBUG] output_ml_trading_signals 开始执行 | 合约: {symbol}")
            print(f"📊 [DEBUG] 输入参数: 预测={prediction:.6f}, 信号值={decision.get('signal_value', 0):.4f}, 价格={current_price:.2f}")
            
            # 获取特征统计信息
            feature_count = len(features.columns) if hasattr(features, 'columns') else 0
            
            # 获取当前持仓情况
            current_pos = self.current_positions.get(symbol, 0)
            pos_status = "多头" if current_pos > 0 else "空头" if current_pos < 0 else "空仓"
            
            # 获取决策相关信息
            signal_value = decision.get('signal_value', 0)
            reason = decision.get('reason', 'N/A')
            
            # 计算预测强度等级
            pred_strength = "强" if abs(prediction) > 0.01 else "中" if abs(prediction) > 0.005 else "弱"
            pred_direction = "看涨" if prediction > 0 else "看跌" if prediction < 0 else "中性"
            
            # 格式化时间戳
            time_str = timestamp.strftime('%Y-%m-%d %H:%M:%S') if hasattr(timestamp, 'strftime') else str(timestamp)
            
            # === 主要交易信号输出 ===
            signal_header = "=" * 80
            SIGNAL_LOGGER.info(signal_header)
            SIGNAL_LOGGER.info(f"🤖 机器学习交易信号 - {symbol} | {time_str}")
            SIGNAL_LOGGER.info(signal_header)
            
            # 模型预测信息
            SIGNAL_LOGGER.info(f"📊 模型预测:")
            SIGNAL_LOGGER.info(f"   预测值: {prediction:.6f} ({pred_direction}, {pred_strength})")
  
            # 价格信息
            SIGNAL_LOGGER.info(f"💰 价格信息:")
            SIGNAL_LOGGER.info(f"   当前价: {current_price:.2f}")
            SIGNAL_LOGGER.info(f"   价格区间: {((high_price - low_price) / current_price * 100):.2f}%")
            
            # 交易决策显示
            signal_emoji = "🟢" if signal_value > 0.001 else "🔴" if signal_value < -0.001 else "⚪"
            SIGNAL_LOGGER.info(f"🎯 交易信号:")
            SIGNAL_LOGGER.info(f"   {signal_emoji} 信号值: {signal_value:.6f}")
            SIGNAL_LOGGER.info(f"   理由: {reason}")
            SIGNAL_LOGGER.info(f"   当前持仓: {current_pos} 手 ({pos_status})")
            
            # 风险提示
            if abs(prediction) > 0.02:
                SIGNAL_LOGGER.info(f"⚠️  强信号警告: 预测值较大 ({prediction:.6f}), 请注意风险控制")
            
            if abs(signal_value) > 0.001:
                SIGNAL_LOGGER.info(f"🚨 交易信号有效: {symbol} 信号值={signal_value:.6f}")
            
            SIGNAL_LOGGER.info(signal_header)
            
            # === 输出到控制台 (简化版本) ===
            console_msg = (
                f"[{time_str}] {symbol} ML信号: "
                f"预测={prediction:.4f}({pred_direction}) | "
                f"信号值={signal_value:.4f} | "
                f"价格={current_price:.2f} | "
                f"持仓={pos_status}"
            )
            print(console_msg)  # 直接输出到控制台
            
            # === 记录到模型预测日志 ===
            MODEL_LOGGER.info(
                f"预测详情 | 合约: {symbol} | 时间: {time_str} | "
                f"预测值: {prediction:.6f} | 信号强度: {signal_value:.6f} | "
                f"特征数: {feature_count} | 信号值: {signal_value:.6f} | 原因: {reason}"
            )
            
            # === 性能统计更新 ===
            if not hasattr(self, 'signal_stats'):
                self.signal_stats = {}
            
            if symbol not in self.signal_stats:
                self.signal_stats[symbol] = {
                    'total_signals': 0,
                    'buy_signals': 0,
                    'sell_signals': 0,
                    'hold_signals': 0,
                    'strong_signals': 0,
                    'last_signal_time': None
                }
            
            stats = self.signal_stats[symbol]
            stats['total_signals'] += 1
            stats['last_signal_time'] = datetime.now()
            
            if signal_value > 0.001:
                stats['buy_signals'] += 1
            elif signal_value < -0.001:
                stats['sell_signals'] += 1
            else:
                stats['hold_signals'] += 1
                
            if abs(prediction) > 0.01:
                stats['strong_signals'] += 1
            
            # 每10个信号输出一次统计
            if stats['total_signals'] % 10 == 0:
                SIGNAL_LOGGER.info(
                    f"📈 信号统计 | {symbol} | "
                    f"总计: {stats['total_signals']} | "
                    f"买入: {stats['buy_signals']} | "
                    f"卖出: {stats['sell_signals']} | "
                    f"持有: {stats['hold_signals']} | "
                    f"强信号: {stats['strong_signals']}"
                )
                
        except Exception as e:
            ERROR_LOGGER.error(f"输出ML交易信号失败 | 合约: {symbol} | 错误: {e}")
            import traceback
            ERROR_LOGGER.error(f"详细错误: {traceback.format_exc()}")

    def day_data_reset(self, data):
        """每日收盘重置数据"""
        from datetime import time as s_time
        # 获取当前时间
        current_time = datetime.now().time()

        # 第一时间范围
        clearing_time1_start = s_time(14,55)
        clearing_time1_end = s_time(15,00)

        # 第二时间范围
        clearing_time2_start = s_time(2,25)
        clearing_time2_end = s_time(2,30)

        current_bid = float(data['BidPrice1'])  # 当前买价
        current_ask = float(data['AskPrice1'])  # 当前卖价
        
        # 检查当前时间第一个操作的时间范围内
        if clearing_time1_start <= current_time <= clearing_time1_end and not self.clearing_executed:
            self.clearing_executed = True  # 设置标志变量为已执行
            
            # 如果有持仓，强制平仓
            has_position = any(pos != 0 for pos in self.current_positions.values())
            if has_position:
                SIGNAL_LOGGER.info("交易日结束，开始强制平仓")
                # 遍历所有合约发送平仓指令
                for instrument_id in list(self.stop_order_dict.keys()):
                    stops = self.stop_order_dict[instrument_id]
                    
                    # 平多仓
                    if stops['long']['position'] > 0:
                        SIGNAL_LOGGER.info(f"发送平多仓指令: {instrument_id}, 手数: {stops['long']['position']}, 价格: {current_bid}")
                        self._insert_order_safe(data['ExchangeID'], data['InstrumentID'], 
                                        current_bid - self.py, 
                                        stops['long']['position'], 
                                        b'1', b'3')
                    # 平空仓
                    if stops['short']['position'] > 0:
                        SIGNAL_LOGGER.info(f"发送平空仓指令: {instrument_id}, 手数: {stops['short']['position']}, 价格: {current_ask}")
                        self._insert_order_safe(data['ExchangeID'], data['InstrumentID'], 
                                        current_ask + self.py, 
                                        stops['short']['position'], 
                                        b'0', b'3')
                
                # 清空所有合约的持仓信息
                for instrument_id in list(self.stop_order_dict.keys()):
                    self.clear_position_info(instrument_id, 'all')
                self.day_closed = True  # 设置日内平仓标志
                SIGNAL_LOGGER.info("日内交易已结束，禁止开新仓")

        # 检查当前时间是否在第二个操作的时间范围内
        elif clearing_time2_start <= current_time <= clearing_time2_end and not self.clearing_executed:
            self.clearing_executed = True  # 设置标志变量为已执行
            
            # 如果有持仓，强制平仓
            has_position = any(pos != 0 for pos in self.current_positions.values())
            if has_position:
                SIGNAL_LOGGER.info("交易日结束，开始强制平仓")
                # 遍历所有合约发送平仓指令
                for instrument_id in list(self.stop_order_dict.keys()):
                    stops = self.stop_order_dict[instrument_id]
                    
                    # 平多仓
                    if stops['long']['position'] > 0:
                        SIGNAL_LOGGER.info(f"发送平多仓指令: {instrument_id}, 手数: {stops['long']['position']}, 价格: {current_bid}")
                        self._insert_order_safe(data['ExchangeID'], data['InstrumentID'], 
                                        current_bid - self.py, 
                                        stops['long']['position'], 
                                        b'1', b'3')
                    # 平空仓
                    if stops['short']['position'] > 0:
                        SIGNAL_LOGGER.info(f"发送平空仓指令: {instrument_id}, 手数: {stops['short']['position']}, 价格: {current_ask}")
                        self._insert_order_safe(data['ExchangeID'], data['InstrumentID'], 
                                        current_ask + self.py, 
                                        stops['short']['position'], 
                                        b'0', b'3')
                
                # 清空所有合约的持仓信息
                for instrument_id in list(self.stop_order_dict.keys()):
                    self.clear_position_info(instrument_id, 'all')
                self.day_closed = True  # 设置日内平仓标志
                SIGNAL_LOGGER.info("日内交易已结束，禁止开新仓")
        else:
            self.clearing_executed = False
            # 在新交易日开始时重置日内平仓标志
            if current_time < clearing_time1_start or (current_time > clearing_time1_end and current_time < clearing_time2_start):
                self.day_closed = False
        return self.clearing_executed
    
    def start_trading(self, symbols):
        """启动交易系统"""
        try:
            SIGNAL_LOGGER.info(f"开始启动交易系统，监控合约: {symbols}")
            
            # 设置交易合约列表
            self.symbols = symbols
            
            # 初始化持仓信息
            for symbol in symbols:
                if symbol not in self.current_positions:
                    self.current_positions[symbol] = 0
                if symbol not in self.stop_order_dict:
                    self.stop_order_dict[symbol] = {
                        'long': {'position': 0, 'entry_price': 0, 'stop_loss': 0, 'take_profit': 0, 'trailing_stop': 0},
                        'short': {'position': 0, 'entry_price': 0, 'stop_loss': 0, 'take_profit': 0, 'trailing_stop': 0}
                    }
            
            # === AlgoPlus TraderApiBase 会自动处理连接，这里只需要等待 ===
            SIGNAL_LOGGER.info(f"等待交易前置连接 | 服务器: {self.td_server}")
            SIGNAL_LOGGER.info("AlgoPlus TraderApiBase 将自动处理连接和登录流程")
            
            # 标记系统为运行状态
            self.is_running = True
            
            SIGNAL_LOGGER.info("交易系统启动成功，等待前置连接...")
            return True
            
        except Exception as e:
            SIGNAL_LOGGER.error(f"启动交易系统失败: {e}")
            return False
    
    def stop_trading(self):
        """停止交易系统"""
        try:
            self.is_running = False
            SIGNAL_LOGGER.info("交易系统已停止")
        except Exception as e:
            SIGNAL_LOGGER.error(f"停止交易系统失败: {e}")
    
    def Join(self):
        """主循环 """
        data = None
        # 记录上次加载止盈止损信息的时间和合约
        last_load_time = {}
        self.cont_df = 0  # 添加这个计数器      
        SIGNAL_LOGGER.info("开始Join主循环，等待行情数据...")       
        while True:
            try:
                # 检查队列是否有数据
                if self.md_queue and not self.md_queue.empty():
                    data = self.md_queue.get(block=False)
                    instrument_id = data['InstrumentID'].decode()  # 品种代码
                    
                    # 定期输出行情接收日志（每100次输出一次，避免日志过多）
                    if not hasattr(self, 'tick_count'):
                        self.tick_count = {}
                    if instrument_id not in self.tick_count:
                        self.tick_count[instrument_id] = 0
                    self.tick_count[instrument_id] += 1
         
                    # 首次运行时加载历史数据（针对当前合约）
                    if hasattr(self, 'kgdata') and self.kgdata:
                        print(f"🔄 [DEBUG] 开始加载历史数据 | 合约: {instrument_id}")
                        # 为当前合约加载历史数据
                        success = self.load_historical_data(instrument_id)
                        if success:
                            # 检查加载的数据量
                            data_count = len(self.bar_data_cache.get(instrument_id, []))
                            print(f"✅ [DEBUG] 历史数据加载成功 | 合约: {instrument_id} | 数据量: {data_count}")
                            SIGNAL_LOGGER.info(f"历史数据加载成功 | 合约: {instrument_id} | 数据量: {data_count}")
                        else:
                            print(f"❌ [DEBUG] 历史数据加载失败 | 合约: {instrument_id}")
                            SIGNAL_LOGGER.warning(f"历史数据加载失败 | 合约: {instrument_id} | 将使用实时数据积累")
                        self.kgdata = False
                    
                    # 加载该合约的止盈止损信息，避免频繁加载
                    current_time = time.time()
                    if instrument_id not in last_load_time or current_time - last_load_time.get(instrument_id, 0) > 60:
                        # 加载止损信息（可选）
                        last_load_time[instrument_id] = current_time
                    
                    # 检查止盈止损条件，需要添加止盈止损逻辑开启
                    # self.check_stop_conditions(data)
                    
                    # 每日收盘重置数据，需要日内平仓开启
                    # self.day_data_reset(data)  
                    
                    # 处理tick数据 
                    tickcome(data)                   
                    # 新K线开始，启动交易程序 
                    global trader_df    
                    if len(trader_df) > self.cont_df and len(trader_df) > 0:
                        print(f"📊 [DEBUG] K线数据更新 | 合约: {instrument_id} | 数据量: {len(trader_df)}")
                        
                        # 保存数据到文件
                        self.save_trading_data(instrument_id, trader_df)
                        
                        # 使用机器学习模型进行预测和交易
                        current_bar_count = len(trader_df)
                        required_bar_count = self.config.min_bar_count
                        
                        # 添加调试日志
                        print(f"🔍 [DEBUG] K线数据检查 | 合约: {instrument_id} | 当前: {current_bar_count} | 需要: {required_bar_count}")
                        SIGNAL_LOGGER.info(f"K线数据检查 | 合约: {instrument_id} | 当前K线数: {current_bar_count} | 需要K线数: {required_bar_count}")
                        
                        if current_bar_count > required_bar_count:
                            print(f"✅ [DEBUG] K线数据充足，开始ML处理 | 合约: {instrument_id}")
                            SIGNAL_LOGGER.info(f"开始ML交易信号处理 | 合约: {instrument_id}")
                            # 如果没有正在运行的ML任务，或上一任务已完成，则提交新的后台任务
                            if self._ml_future is None or self._ml_future.done():
                                # 为避免多线程下 DataFrame 被修改，传递拷贝
                                df_copy = trader_df.copy()
                                # 保存当前tick以供线程使用，然后后台执行计算
                                self._current_tick_data = data
                                self._ml_future = self.executor.submit(self.process_ml_trading_signals, instrument_id, df_copy)
                            else:
                                SIGNAL_LOGGER.debug(f"ML任务仍在执行，跳过本次触发 | 合约: {instrument_id}")
                        
                        SIGNAL_LOGGER.debug(f"{instrument_id} 当前数据: {trader_df['datetime'].iloc[-1] if len(trader_df) > 0 else 'No data'}")
                        self.cont_df = len(trader_df)
                        
                else:
                    # 队列为空时休眠一下
                    time.sleep(0.1)
                    
                    # 添加心跳日志 - 每60秒输出一次系统状
                    current_time = time.time()
                    if not hasattr(self, 'last_heartbeat_time'):
                        self.last_heartbeat_time = current_time
                    elif current_time - self.last_heartbeat_time >= 60:  # 60秒心跳
                        # 统计当前系统状态
                        active_positions = len([pos for pos in self.current_positions.values() if pos != 0])
                        total_tick_count = sum(getattr(self, 'tick_count', {}).values()) if hasattr(self, 'tick_count') else 0
                        
                        SYSTEM_LOGGER.info(f"系统运行正常 | 时间: {datetime.now().strftime('%H:%M:%S')} | 活跃持仓: {active_positions} | 接收tick数: {total_tick_count}")
                        self.last_heartbeat_time = current_time
                        
                        # 强制刷新所有日志缓冲区
                        self.force_flush_all_logs()
                    
            except Exception as e:
                SIGNAL_LOGGER.error(f"Join循环处理出错: {e}")
                time.sleep(1)
    
    def force_flush_all_logs(self):
        """强制刷新所有日志缓冲区，确保日志及时写入文件"""
        try:
            loggers = [SYSTEM_LOGGER, SIGNAL_LOGGER, DATA_LOGGER, MODEL_LOGGER, 
                      RISK_LOGGER, ORDER_LOGGER, POSITION_LOGGER, PERFORMANCE_LOGGER,
                      LEARNING_LOGGER, STOPLOSS_LOGGER, COMPLIANCE_LOGGER, ERROR_LOGGER]
            
            for logger in loggers:
                for handler in logger.handlers:
                    handler.flush()
                    # 强制刷新文件流
                    if hasattr(handler, 'stream') and hasattr(handler.stream, 'flush'):
                        handler.stream.flush()
                        
        except Exception as e:
            print(f"强制刷新日志失败: {e}")
    
    def _resolve_exchange_id(self, exchange_id_bytes, instrument_id_bytes):
        """确保ExchangeID有效，若为空则根据合约代码推断并返回bytes"""
        if exchange_id_bytes and (exchange_id_bytes.strip() if isinstance(exchange_id_bytes, bytes) else exchange_id_bytes):
            return exchange_id_bytes
        try:
            symbol = instrument_id_bytes.decode() if isinstance(instrument_id_bytes, bytes) else str(instrument_id_bytes)
            ex_id = self.get_exchange_id(symbol)
            return ex_id.encode()
        except Exception as e:
            ERROR_LOGGER.error(f"解析ExchangeID失败 | 错误: {e}")
            return b'SHFE'

    def _insert_order_safe(self, exchange_id, instrument_id, price, volume, direction, offset_flag):
        """包装 insert_order：自动补全 ExchangeID 并记录返回码"""
        ex_id = self._resolve_exchange_id(exchange_id, instrument_id)
        ret = self.insert_order(ex_id, instrument_id, price, volume, direction, offset_flag)
        if ret != 0:
            ORDER_LOGGER.error(
                f"❌ insert_order 返回非0({ret}) | 合约: {instrument_id} | ExID: {ex_id} | 价格: {price} | 方向: {direction} | 开平: {offset_flag}")
        return ret
    
    
# 使用统一配置
Config = StrategyConfig

# === 实盘复权处理函数（来自adjust.py算法） ===
def apply_adjustment_factors_for_realtime(data):
    """
    对实盘合约K线数据做复权处理，算法与adjust.py一致。
    data: DataFrame，包含['open','high','low','close','symbol']等字段
    返回：复权后的DataFrame
    """
    data = data.copy()
    # 如果有_id, index, date字段，先去掉
    for col in ['_id', 'index', 'date']:
        if col in data.columns:
            data.drop(col, axis=1, inplace=True)
    data = data.reset_index(drop=True)
    # 检查小数位数
    first_row = data.iloc[0]
    def check_decimal_places(value):
        if pd.isna(value):
            return 0
        value_str = f"{value:.10f}"
        return len(value_str.split('.')[1].rstrip('0'))
    decimal_places = max(
        check_decimal_places(first_row['open']),
        check_decimal_places(first_row['high']),
        check_decimal_places(first_row['low']),
        check_decimal_places(first_row['close'])
    )
    # 计算复权因子
    factors = pd.Series(1, index=data.index)
    contract_changes = data['symbol'] != data['symbol'].shift(1)
    factors[contract_changes] = data['close'].shift(1) / data['open'][contract_changes]
    factors = factors.cumprod()
    factors.iloc[0] = 1
    # 应用复权
    data[['open', 'high', 'low', 'close']] = data[['open', 'high', 'low', 'close']].multiply(factors, axis=0).round(decimal_places)
    data['rbt'] = factors
    return data

# -----------------------------------------------------------------
# 后台执行 ML 信号计算的包装函数，供线程池调用
# -----------------------------------------------------------------
def _run_ml_process(symbol, data_df, tick_data):
    # 该函数已迁移到 MyTrader 类中执行，此处保留空实现以避免重复定义
    pass

if __name__ == "__main__":
    main()
