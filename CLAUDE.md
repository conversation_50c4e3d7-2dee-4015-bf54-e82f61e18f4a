# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is an **Enhanced LightGBM Futures Trading System** (v2.1) that implements a machine learning-based algorithmic trading strategy for futures markets using the CTP (Comprehensive Transaction Platform) interface. The system is designed with separate modules for backtesting and live trading, unified by a shared configuration system.

## Running the System

The system consists of three main Python files with integrated menu systems:

```bash
# Run the live trading system (CTP interface)
python3 ml_ctp.py

# Run the backtesting system (VnPy integration)  
python3 ml_bt.py

# View/modify unified configuration
python3 strategy_config.py

# Both live and backtest systems include interactive menus for all operations
# Always run comprehensive testing before live trading
```

## High-Level Architecture

### System Components

This trading system uses a **modular architecture** with three main components:

1. **Live Trading System** (`ml_ctp.py`, ~3153 lines)
   - CTP interface integration via AlgoPlus.CTP
   - Real-time tick data processing and K-line generation
   - LightGBM model inference for trading signals
   - Risk management system with multi-layer controls
   - Dynamic learning manager with online retraining
   - Comprehensive logging system with 12 specialized loggers
   - Integrated menu system for testing and operation

2. **Backtesting System** (`ml_bt.py`, ~1233 lines)
   - VnPy framework integration for historical testing
   - Integrated DataAPI for futures data download
   - Feature engineering pipeline matching live system
   - Model training with hyperparameter optimization
   - Complete backtesting with performance metrics
   - Strategy optimization capabilities

3. **Unified Configuration** (`strategy_config.py`, ~296 lines)
   - Single source of truth for all system parameters
   - Ensures consistency between backtesting and live trading
   - Risk management parameters and safety limits
   - Model parameters and feature engineering settings
   - Account configuration for both SimNow and live trading

### Architecture Principles

- **Configuration Consistency**: Both systems import from `StrategyConfig` class
- **Feature Parity**: Same feature engineering between backtest and live
- **Risk-First Design**: Multiple safety layers and automatic protections
- **Modular Structure**: Clear separation between data, model, and trading logic

### Key Dependencies

**Live Trading System Requirements:**
- `AlgoPlus.CTP`: CTP trading interface for futures connectivity
- `lightgbm`: Machine learning model framework
- `polars`: High-performance data processing (with pandas fallback)
- `scikit-learn`: Model validation and cross-validation
- `numpy`: Numerical computations and array operations

**Backtesting System Requirements:**
- `vnpy.trader` and `vnpy.alpha`: VnPy trading framework for backtesting
- `requests`: HTTP client for DataAPI data downloads
- All live system dependencies above

**Shared Configuration:**
- `strategy_config.StrategyConfig`: Imported by both systems for parameter consistency

### Configuration Management

The `StrategyConfig` class in `strategy_config.py` provides centralized parameter management:

**Core Strategy Parameters:**
- Trading thresholds: `open_threshold`, `close_threshold`, `stop_loss`, `profit_target`
- Position limits: `max_positions`, `max_holding_days`
- Risk controls: `max_position_ratio` (20%), `max_daily_loss_ratio` (5%)

**Key Safety Parameters:**
- `min_available_margin_ratio`: 30% minimum margin requirement
- `max_orders_per_day`: Daily order limit protection
- `max_position_per_symbol`: Per-instrument position limits

**Account Configuration:**
- `simnow_account`: SimNow simulation credentials and settings
- `real_account`: Live trading account configuration template
- `dataapi_config`: Data download service credentials

### File Structure Created at Runtime

```
./models/           # Trained LightGBM models and versions
./data/            # Historical K-line data (CSV format)
./logs/            # Comprehensive logging system
├── system.log
├── signal.log
├── risk.log
└── [8 more specialized log files]
./traderdata/      # Position and stop-loss state persistence
./features/        # Feature sets and model metadata
```

## Development Workflow

### Complete Trading System Development

**1. Strategy Development and Testing:**
```bash
# Start with backtesting system
python3 ml_bt.py
# Menu options for data download, model training, and backtesting

# Test configuration consistency
python3 strategy_config.py
# Validates parameter consistency between systems
```

**2. Live System Preparation:**
```bash
# Run live trading system
python3 ml_ctp.py
# Choose comprehensive system testing (usually menu option 9)
# Verify all safety systems before live deployment
```

**3. Configuration Modification:**
- Edit `strategy_config.py` to adjust risk parameters, thresholds, and account settings
- Both systems automatically use updated configuration
- Run `StrategyConfig.validate_config()` to check parameter consistency

### System Testing Workflow

**Critical Pre-Trading Checklist:**
1. Run backtesting system to validate strategy performance
2. Test live system components individually (menu options 2-4)  
3. Run comprehensive system test (menu option 9)
4. Verify all safety systems and risk controls
5. Test on SimNow simulation before live trading

## Key Implementation Details

### Live Trading System Architecture (`ml_ctp.py`)

**Core Classes and Functions:**
- `setup_logging()`: Comprehensive logging system with 12 specialized loggers
- `calculate_features()`: 100+ technical indicators with robust error handling
- `MyTrader`: Main trading class inheriting from `TraderApiBase`
- `ModelManager`: Dynamic model loading and version management
- `RiskManager`: Real-time risk monitoring and position control

**Risk Management Features:**
- Pre-trade fund availability and position limit checks
- Real-time monitoring with periodic status updates
- Automatic trade blocking when safety limits exceeded
- Emergency position closing at market close

**Data Processing Pipeline:**
- Tick data validation and error recovery
- Efficient K-line generation with memory management
- Polars-first approach with Pandas fallback
- Persistent data storage with integrity checks

### Backtesting System Architecture (`ml_bt.py`)

**Key Components:**
- `DataDownloader`: Integrated DataAPI client for historical data
- `get_data_filename()`: Unified file naming for consistency
- VnPy `AlphaStrategy` integration for realistic backtesting
- Hyperparameter optimization with `OptimizationSetting`

**Feature Engineering Consistency:**
- Same technical indicators as live system
- Matching data preprocessing and normalization
- Identical model training pipeline
- Cross-validation for robust model selection

## Integration Points

**Live Trading Integrations:**
- **CTP Brokers**: Via AlgoPlus.CTP for real-time futures trading
- **SimNow**: Simulation environment with identical production code
- **Internal Systems**: Risk management, logging, and model management

**Backtesting Integrations:**
- **VnPy Framework**: Professional backtesting environment
- **DataAPI Service**: Historical futures data downloads via HTTP API
- **Model Training**: LightGBM with scikit-learn cross-validation

**Shared Integrations:**
- **Configuration System**: `StrategyConfig` ensures parameter consistency
- **Feature Engineering**: Identical processing pipelines across systems
- **Model Format**: Compatible LightGBM model serialization

## Safety and Risk Management

### Configuration Validation
```python
# Always validate configuration before trading
errors = StrategyConfig.validate_config()
if errors:
    print("Configuration errors:", errors)
```

### Multi-Layer Protection
- **Parameter Validation**: Automatic checks for reasonable trading parameters
- **Risk Limits**: Hard-coded maximum position and loss limits
- **Real-time Monitoring**: Continuous account and position tracking  
- **Emergency Stops**: Automatic position closure at market close

### Account Safety
- **SimNow Testing**: Mandatory simulation testing before live trading
- **Credential Management**: Separate configs for simulation and live accounts
- **Gradual Deployment**: Start with minimal position sizes

This modular system ensures consistency between backtesting and live trading while maintaining enterprise-grade safety and risk management features.