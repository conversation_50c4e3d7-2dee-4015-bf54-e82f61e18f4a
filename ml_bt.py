"""
LightGBM期货交易系统统一回测系统（完整版）
集成数据下载、特征工程、模型训练和回测的完整流程

"""

from datetime import datetime, timedelta
from collections import defaultdict, deque
import os
import numpy as np
import polars as pl
import pandas as pd
from tqdm import tqdm
import logging
import sys
import pickle
import warnings
from pathlib import Path
from functools import partial
import copy
import requests
from io import StringIO

# VnPy相关导入
from vnpy.trader.constant import Interval, Direction, Offset
from vnpy.trader.object import BarData, TradeData, OrderData
from vnpy.trader.utility import extract_vt_symbol, round_to
from vnpy.alpha import AlphaStrategy, AlphaLab, AlphaDataset, AlphaModel, Segment
from vnpy.alpha.model.models.lgb_model import LgbModel
from vnpy.alpha.strategy.backtesting import BacktestingEngine
from vnpy.trader.optimize import OptimizationSetting
from strategy_config import StrategyConfig

warnings.filterwarnings("ignore", category=FutureWarning)

# =============================================================================
# 统一数据文件名生成函数
# =============================================================================
def get_data_filename(symbol, start_date, end_date, kline_period, adjust_type):
    """
    统一生成数据文件名，保证下载和读取一致
    """
    return f"{symbol}_{start_date}_{end_date}_{kline_period}_adj{adjust_type}.csv"

# =============================================================================
# 数据下载模块（集成DataAPI功能）
# =============================================================================

class DataDownloader:
    """
    期货数据下载器 - 集成DataAPI功能
    """
    
    def __init__(self, username=None, password=None, base_url=None):
        """
        初始化数据下载器
        
        参数:
            username: API用户名
            password: API密码  
            base_url: API基础URL
        """
        self.username = username or os.getenv('DATAAPI_USERNAME')
        self.password = password or os.getenv('DATAAPI_PASSWORD')
        self.base_url = base_url or os.getenv('DATAAPI_URL', 'http://kanpan789.com:8086/ftdata')
        
        if not all([self.username, self.password]):
            print("⚠️  警告: 未提供DataAPI认证信息，将无法下载数据")
            print("可以通过以下方式提供认证信息：")
            print("1. 环境变量: DATAAPI_USERNAME, DATAAPI_PASSWORD")
            print("2. 直接传参: DataDownloader(username='xxx', password='xxx')")
    
    def download_futures_data(self, symbol, start_date, end_date, kline_period='5m', adjust_type='0', depth='no'):
        """
        下载期货数据
        
        参数:
            symbol: 合约代码，如 'au2506' 或 ['au2506', 'ag2506']
            start_date: 开始日期 '2023-01-01'
            end_date: 结束日期 '2024-12-31'
            kline_period: K线周期 '1m', '5m', '15m', '1h', '1d'
            adjust_type: 复权类型 '0'(不复权) '1'(后复权)
            depth: 是否获取深度数据 'yes' 或 'no'
            
        返回:
            pandas.DataFrame: 包含OHLCV数据的DataFrame
        """
        if not all([self.username, self.password]):
            raise ValueError("缺少API认证信息，无法下载数据")
        
        # 构建请求参数
        params = {
            'username': self.username,
            'password': self.password,
            'symbol': symbol,
            'start_date': start_date,
            'end_date': end_date,
            'kline_period': kline_period,
            'adjust_type': adjust_type
        }
        
        if depth:
            params['Depth'] = depth
        
        print(f"📥 正在下载数据: {symbol} ({start_date} 至 {end_date})")
        print(f"📊 K线周期: {kline_period}, 复权类型: {adjust_type}")
        
        try:
            # 发送请求
            response = requests.get(self.base_url, params=params, timeout=300)
            
            # 检查响应状态
            if response.status_code == 200:
                # 检查响应是否为JSON格式
                if 'application/json' in response.headers.get('Content-Type', ''):
                    data = pd.read_json(StringIO(response.text), orient='records')
                    data = data.reset_index()
                    
                    # 列名排序
                    if depth == 'yes':
                        columns = ['datetime', 'symbol', 'open', 'high', 'low', 'close', 'volume', 'amount', 'openint', 
                                 'cumulative_openint',  
                                 '开仓', '平仓', '多开', '空开', '多平', '空平', '双开', '双平', '双换', 'B', 'S', '未知']
                    else:
                        columns = ['datetime', 'symbol', 'open', 'high', 'low', 'close', 'volume', 'amount', 'openint',
                                 'cumulative_openint']
                    
                    # 重新排列列名（只保留存在的列）
                    available_columns = [col for col in columns if col in data.columns]
                    data = data.reindex(columns=available_columns)
                    
                    # 处理时间列
                    data['datetime'] = pd.to_datetime(data['datetime'])
                    
                    # 转换为本地时区
                    if data['datetime'].dt.tz is not None:
                        data['datetime'] = data['datetime'].dt.tz_convert('Asia/Shanghai')
                    else:
                        data['datetime'] = data['datetime'].dt.tz_localize('Asia/Shanghai')
                    
                    # 格式化时间显示
                    data['datetime'] = data['datetime'].dt.strftime('%Y-%m-%d %H:%M:%S')
                    
                    print(f"✅ 数据下载成功: {len(data)} 条记录")
                    return data
                else:
                    print(f"❌ 响应不是JSON格式: {response.text[:500]}")
                    return None
            elif response.status_code == 401:
                print("❌ 认证失败：用户名和密码不能为空")
                return None
            elif response.status_code == 402:
                print("❌ 认证失败：账号不存在，请检查账号")
                return None
            elif response.status_code == 405:
                print("❌ 认证失败：账号已过期")
                return None
            elif response.status_code == 406:
                print("❌ 认证失败：密码错误")
                return None
            else:
                print(f"❌ 请求失败，状态码：{response.status_code}")
                if response.headers.get('Content-Type', '').startswith('application/json'):
                    error_info = response.json().get('error', '未知错误')
                    print(f"错误信息：{error_info}")
                return None
                
        except Exception as e:
            print(f"❌ 下载数据时发生错误：{e}")
            return None
    
    def save_data(self, data, symbol, start_date, end_date, kline_period, save_dir='./data', adjust_type='0'):
        """
        保存数据到文件
        
        参数:
            data: DataFrame数据
            symbol: 合约代码
            start_date: 开始日期
            end_date: 结束日期
            kline_period: K线周期
            save_dir: 保存目录
            adjust_type: 复权类型
        返回:
            str: 保存的文件路径
        """
        if data is None or len(data) == 0:
            print("❌ 没有数据可保存")
            return None
        
        # 创建保存目录
        os.makedirs(save_dir, exist_ok=True)
        
        # 统一生成文件名
        filename = get_data_filename(symbol, start_date, end_date, kline_period, adjust_type)
        filepath = os.path.join(save_dir, filename)
        
        # 保存到CSV
        try:
            data.to_csv(filepath, index=False, encoding='utf-8')
            print(f"💾 数据已保存到: {filepath}")
            return filepath
        except Exception as e:
            print(f"❌ 保存数据失败: {e}")
            return None
    
    def download_and_save(self, symbol, start_date, end_date, kline_period='5m', save_dir='./data', adjust_type='0'):
        """
        下载并保存数据的便捷方法
        
        参数:
            symbol: 合约代码
            start_date: 开始日期
            end_date: 结束日期
            kline_period: K线周期
            save_dir: 保存目录
            adjust_type: 复权类型
        返回:
            tuple: (数据DataFrame, 文件路径)
        """
        # 下载数据
        data = self.download_futures_data(
            symbol=symbol,
            start_date=start_date,
            end_date=end_date,
            kline_period=kline_period,
            adjust_type=adjust_type
        )
        
        # 保存数据
        if data is not None:
            filepath = self.save_data(data, symbol, start_date, end_date, kline_period, save_dir, adjust_type)
            return data, filepath
        
        return None, None

# =============================================================================
# 数据处理函数
# =============================================================================

def advanced_process_missing_values(df: pl.DataFrame) -> pl.DataFrame:
    """
    综合缺失值处理策略，包含多种技术
    """
    # 处理每个数值特征列
    numeric_columns = df.columns[2:-1]  # 跳过日期、代码和标签列
    
    for col in numeric_columns:
        # 步骤1：检查缺失比例
        missing_ratio = (df[col].null_count() + df.filter(pl.col(col).is_nan()).height) / df.height
        
        if missing_ratio > 0.5:
            # 缺失过多，使用全局统计量
            df = df.with_columns(pl.col(col).fill_null(pl.col(col).median()))
        elif missing_ratio > 0.25:
            # 中等程度缺失，尝试前向填充然后后向填充
            df = df.with_columns(pl.col(col).forward_fill().backward_fill())
            # 对于剩余的空值，使用中位数
            df = df.with_columns(pl.col(col).fill_null(pl.col(col).median()))
        else:
            # 少量缺失，使用类似插值的方法
            # 首先前向填充，然后后向填充
            df = df.with_columns(pl.col(col).forward_fill().backward_fill())
            # 对于剩余的空值，用中位数填充
            df = df.with_columns(pl.col(col).fill_null(pl.col(col).median()))
    
    return df


def enhance_process_drop_na(df: pl.DataFrame) -> pl.DataFrame:
    """
    移除含有缺失值的行
    同时确保不会删除过多数据
    """
    # 计算每列的缺失值比例
    column_missing_ratio = {}
    for col in df.columns[2:-1]:  # 跳过日期和合约代码，以及标签列
        null_count = df[col].null_count()
        # 计算NaN的数量
        nan_count = df.filter(pl.col(col).is_nan()).height
        missing_count = null_count + nan_count
        column_missing_ratio[col] = missing_count / df.height
    
    # 找出缺失值比例低于25%的列
    good_columns = [col for col, ratio in column_missing_ratio.items() if ratio < 0.25]
    
    # 对这些列进行缺失值移除
    if good_columns:
        print(f"仅使用缺失值较少的 {len(good_columns)}/{len(df.columns)-3} 列进行训练")
        df = df.drop_nulls(subset=good_columns)
    else:
        # 如果所有列都有大量缺失，则全部保留并在下一步填充
        print("所有特征列都有大量缺失值，保留所有数据进行填充")
    
    return df


def enhance_process_fill_na(df: pl.DataFrame) -> pl.DataFrame:
    """
    优化的缺失值填充策略
    """
    numeric_columns = df.columns[2:-1]  # 跳过日期和合约代码，以及标签列
    
    for col in numeric_columns:
        # 改用更安全的方法：先用后向填充，再用均值填充
        df = df.with_columns(pl.col(col).backward_fill().fill_null(pl.col(col).mean()))
    return df


def process_time_series_norm(df: pl.DataFrame, window_size: int = 20) -> pl.DataFrame:
    """
    使用滚动窗口为每个符号进行时间序列标准化
    """
    feature_columns = df.columns[2:-1]  # 跳过日期、代码和标签列
    
    for col in feature_columns:
        # 为每个符号计算滚动平均值和标准差
        df = df.with_columns([
            pl.col(col).rolling_mean(window_size).over(["vt_symbol"]).alias(f"{col}_mean"),
            pl.col(col).rolling_std(window_size).over(["vt_symbol"]).alias(f"{col}_std")
        ])
        
        # 应用z-score标准化
        df = df.with_columns(
            ((pl.col(col) - pl.col(f"{col}_mean")) / (pl.col(f"{col}_std") + 1e-8)).alias(col)
        ).drop([f"{col}_mean", f"{col}_std"])
    
    return df


def process_winsorize(df: pl.DataFrame, lower_quantile: float = 0.01, upper_quantile: float = 0.99) -> pl.DataFrame:
    """
    温莎化数据以减少异常值的影响
    """
    feature_columns = df.columns[2:-1]  # 跳过日期、代码和标签列
    
    for col in feature_columns:
        # 计算每个特征的分位数
        lower_bound = df[col].quantile(lower_quantile)
        upper_bound = df[col].quantile(upper_quantile)
        
        # 温莎化（将值限制在分位数阈值内）
        df = df.with_columns(
            pl.col(col).clip(lower_bound, upper_bound).alias(col)
        )
    
    return df

# =============================================================================
# 数据转换工具
# =============================================================================

def convert_dataapi_to_vnpy_format(data, vt_symbol):
    """
    将DataAPI格式的数据转换为VnPy AlphaLab格式
    
    参数:
        data: DataAPI下载的DataFrame
        vt_symbol: VnPy格式的合约代码，如 "au888.5M.SHFE"
        
    返回:
        pl.DataFrame: VnPy AlphaLab格式的数据
    """
    if data is None:
        return None
    
    try:
        # 确保有必要的列
        required_cols = ['datetime', 'open', 'high', 'low', 'close', 'volume']
        missing_cols = [col for col in required_cols if col not in data.columns]
        
        if missing_cols:
            print(f"❌ 缺少必要的列: {missing_cols}")
            return None
        
        # 创建VnPy格式的DataFrame
        vnpy_data = pd.DataFrame({
            'datetime': pd.to_datetime(data['datetime']),
            'vt_symbol': vt_symbol,
            'open': pd.to_numeric(data['open'], errors='coerce'),
            'high': pd.to_numeric(data['high'], errors='coerce'),
            'low': pd.to_numeric(data['low'], errors='coerce'),
            'close': pd.to_numeric(data['close'], errors='coerce'),
            'volume': pd.to_numeric(data['volume'], errors='coerce').fillna(0),
        })
        
        # 如果有成交额数据，也包含进来
        if 'amount' in data.columns:
            vnpy_data['turnover'] = pd.to_numeric(data['amount'], errors='coerce').fillna(0)
        else:
            vnpy_data['turnover'] = vnpy_data['volume'] * vnpy_data['close']
        
        # 如果有持仓量数据
        if 'openint' in data.columns:
            vnpy_data['open_interest'] = pd.to_numeric(data['openint'], errors='coerce').fillna(0)
        else:
            vnpy_data['open_interest'] = 0
        
        # 删除包含NaN的行
        vnpy_data = vnpy_data.dropna(subset=['open', 'high', 'low', 'close'])
        
        # 转换为polars DataFrame
        pl_data = pl.from_pandas(vnpy_data)
        
        print(f"✅ 数据格式转换成功: {len(pl_data)} 条记录")
        return pl_data
        
    except Exception as e:
        print(f"❌ 数据格式转换失败: {e}")
        return None

# =============================================================================
# 增强数据集类
# =============================================================================

class EnhancedFuturesMlDataset(AlphaDataset):
    """
    单品种期货时序数据集
    """
    
    def __init__(
        self,
        df: pl.DataFrame,
        train_period: tuple[str, str],
        valid_period: tuple[str, str],
        test_period: tuple[str, str],
        lookback_window: int = 20,
        predict_window: int = 3
    ) -> None:
        """构造函数"""
        super().__init__(
            df=df,
            train_period=train_period,
            valid_period=valid_period,
            test_period=test_period,
        )
        
        self.lookback_window = lookback_window
        self.predict_window = predict_window
        
        # 基本价格特征
        for w in [1, 2, 3, 5, 10, 20, 30]:
             # 收益率特征
            self.add_feature(f"ret_{w}", f"close / ts_delay(close, {w}) - 1")
            
            # 移动平均线特征
            self.add_feature(f"ma_{w}", f"ts_mean(close, {w}) / close")
            
            # 高低点特征
            self.add_feature(f"high_ratio_{w}", f"ts_max(high, {w}) / close")
            self.add_feature(f"low_ratio_{w}", f"close / ts_min(low, {w})")
            
            # 价格波动特征
            self.add_feature(f"std_{w}", f"ts_std(close, {w}) / close")
            
            # 价格位置特征
            self.add_feature(f"price_position_{w}", f"(close - ts_min(low, {w})) / (ts_max(high, {w}) - ts_min(low, {w}) + 1e-6)")
            
            # 价格与移动平均线的比率
            self.add_feature(f"close_to_ma_{w}", f"close / ts_mean(close, {w})")
        
        # K线形态特征
        self.add_feature("body_ratio", "(close - open) / (high - low + 1e-6)")
        self.add_feature("upper_shadow", "(high - ts_greater(open, close)) / (high - low + 1e-6)")
        self.add_feature("lower_shadow", "(ts_less(open, close) - low) / (high - low + 1e-6)")
        
        # 波动范围指标
        self.add_feature("daily_range", "(high - low) / open")
        
        # 动量指标
        for w in [5, 10, 20]:
            # RSI指标
            self.add_feature(f"rsi_{w}", f"100 * ts_mean(close > ts_delay(close, 1), {w})")
            
            # MACD相关指标
            self.add_feature(f"ema_short_{w}", f"ts_mean(close, {w//2}) / close")
            self.add_feature(f"ema_long_{w}", f"ts_mean(close, {w}) / close")
            self.add_feature(f"macd_{w}", f"ts_mean(close, {w//2}) / ts_mean(close, {w}) - 1")
                
        # 波动率指标
        for w in [5, 10, 20]:
            self.add_feature(f"atr_{w}", f"ts_mean((high - low) / close, {w})")
            
            # 波动率突破指标
            self.add_feature(f"volatility_breakout_{w}", f"(close - ts_mean(close, {w})) / (ts_std(close, {w}) + 1e-6)")
        
        # 成交量特征
        for w in [5, 10, 20]:
            # 成交量趋势
            self.add_feature(f"vol_ma_{w}", f"volume / ts_mean(volume, {w})")
            self.add_feature(f"vol_ratio_{w}", f"ts_mean(volume, {w}) / volume")
            
            # 成交量波动
            self.add_feature(f"vol_std_{w}", f"ts_std(volume, {w}) / ts_mean(volume, {w})")
            self.add_feature(f"vol_std_orig_{w}", f"ts_std(volume, {w}) / volume")
            
            # OBV指标近似
            self.add_feature(f"volume_price_corr_{w}", f"ts_corr(close, volume, {w})")
        
        # 交易量价比特征
        self.add_feature("turnover_ratio", "turnover / (volume * close + 1e-6)")
        
        # 趋势强度指标
        for w in [5, 10, 20, 30, 60]:
            # 上涨天数比例
            self.add_feature(f"up_days_ratio_{w}", f"ts_sum(close > ts_delay(close, 1), {w}) / {w}")
            
            # 趋势方向指标
            self.add_feature(f"adx_{w}", f"ts_abs(ts_sum(close > ts_delay(close, 1), {w}) / {w} - 0.5) * 2")

            self.add_feature(f"resi_{w}", f"ts_resi(close, {w}) / close")
            self.add_feature(f"qtlu_{w}", f"ts_quantile(close, {w}, 0.8) / close")
            self.add_feature(f"qtll_{w}", f"ts_quantile(close, {w}, 0.2) / close")
        
        # 目标变量 - 未来收益率
        self.set_label(f"(ts_delay(close, -{predict_window}) / close - 1) * 100")


# =============================================================================
# 特征选择函数
# =============================================================================

def feature_selection(dataset, model, importance_threshold=0.01, max_features=None, method='split'):
    """
    基于LightGBM模型的特征重要性进行特征选择
    """
    import numpy as np
    import pandas as pd
    
    # 确保模型已经训练过
    if not hasattr(model, 'model') or model.model is None:
        raise ValueError("模型未训练，无法获取特征重要性")
    
    # 获取LightGBM原生模型
    lgb_model = model.model
    
    # 从数据集中获取特征名称
    df_train = dataset.fetch_learn(Segment.TRAIN)
    feature_names = df_train.columns[2:-1]  # 跳过日期、代码和标签列
    
    # 获取特征重要性
    if method == 'split':
        importances = lgb_model.feature_importance('split')
    elif method == 'gain':
        importances = lgb_model.feature_importance('gain')
    else:
        raise ValueError("method参数必须是'split'或'gain'")
    
    # 归一化特征重要性
    normalized_importances = importances / np.sum(importances)
    
    # 创建特征重要性字典
    feature_importance_dict = {name: importance for name, importance in zip(feature_names, normalized_importances)}
    
    # 按重要性排序
    sorted_features = sorted(
        feature_importance_dict.items(), 
        key=lambda x: x[1], 
        reverse=True
    )
    
    # 创建并输出特征重要性DataFrame
    importance_df = pd.DataFrame(sorted_features, columns=['feature', 'importance'])
    importance_df['cumulative_importance'] = importance_df['importance'].cumsum()
    
    print("特征重要性分析:")
    print(importance_df.head(20))
    
    # 应用阈值和最大特征数量进行筛选
    selected_features = []
    for feature, importance in sorted_features:
        if importance >= importance_threshold:
            selected_features.append(feature)
    
    # 如果指定了最大特征数量，则进一步限制
    if max_features is not None and len(selected_features) > max_features:
        selected_features = selected_features[:max_features]
    
    print(f"从 {len(feature_names)} 个特征中选择了 {len(selected_features)} 个特征")
    print(f"选择的特征覆盖了 {importance_df[importance_df['feature'].isin(selected_features)]['importance'].sum()*100:.2f}% 的总重要性")
    
    # 创建新数据集
    new_dataset = copy.deepcopy(dataset)
    
    # 筛选特征表达式
    original_expressions = dataset.feature_expressions.copy()
    new_dataset.feature_expressions = {name: expr for name, expr in original_expressions.items() if name in selected_features}
    
    # 重新准备数据集
    print("正在重新准备数据集...")
    new_dataset.prepare_data()
    print("数据集准备完成")
    
    return new_dataset, feature_importance_dict




# =============================================================================
# 简单信号策略（兼容性保持）
# =============================================================================

class SignalStrategy(AlphaStrategy):
    """基于信号的简单策略"""
    
    def __init__(self, strategy_engine, strategy_name, vt_symbols, setting):
        super().__init__(strategy_engine, strategy_name, vt_symbols, setting)
        self.signal_dict = {}
        self.strategy_engine = strategy_engine
            
        # 策略参数
        self.open_threshold = setting.get("open_threshold", 0.002)
        self.close_threshold = setting.get("close_threshold", 0.001)
    
    def on_init(self):
        """策略初始化回调"""
        self._process_signal_data()
        self.write_log("策略初始化完成")
    
    def _process_signal_data(self):
        """处理信号数据，构建查找字典"""
        if not hasattr(self.strategy_engine, "signal_df"):
            self.write_log("警告: 引擎没有signal_df属性")
            return
        
        signal_df = self.strategy_engine.signal_df
        
        # 针对polars DataFrame的处理
        if hasattr(signal_df, 'to_pandas'):
            try:
                signal_df = signal_df.to_pandas()
            except Exception as e:
                self.write_log(f"转换失败：{e}")
                return
        
        # 检查是否有必要的列
        if "datetime" not in signal_df.columns or "vt_symbol" not in signal_df.columns or "signal" not in signal_df.columns:
            self.write_log(f"信号数据缺少必要的列，现有列：{signal_df.columns}")
            return
        
        # 处理pandas DataFrame
        try:
            for _, row in signal_df.iterrows():
                dt = row["datetime"]
                symbol = row["vt_symbol"]
                if symbol not in self.signal_dict:
                    self.signal_dict[symbol] = {}
                
                # 使用datetime对象作为键
                if isinstance(dt, str):
                    dt = datetime.strptime(dt, "%Y-%m-%d %H:%M:%S")
                
                # 格式化为字符串便于查找
                dt_key = dt.strftime("%Y-%m-%d %H:%M:%S")
                self.signal_dict[symbol][dt_key] = row["signal"]
                
            self.write_log(f"成功处理信号数据，共{len(signal_df)}行")
        except Exception as e:
            self.write_log(f"处理信号数据时出错：{e}")
    
    def on_bars(self, bars):
        """收到K线更新时的回调"""
        # 取消所有活跃订单
        self.cancel_all()
        
        for vt_symbol, bar in bars.items():
            # 获取当前持仓
            current_pos = self.get_pos(vt_symbol)
            
            # 获取当前时间点的信号
            dt = bar.datetime.strftime("%Y-%m-%d %H:%M:%S")
            signal_value = self.signal_dict.get(vt_symbol, {}).get(dt, 0)
            
            # 根据信号和当前持仓判断交易方向
            if current_pos == 0:  # 当前无持仓
                if signal_value > self.open_threshold:  # 开多
                    self.buy(vt_symbol, bar.close_price, 1)
                    self.write_log(f"开多: {vt_symbol}, 信号值: {signal_value:.4f}")
                elif signal_value < -self.open_threshold:  # 开空
                    self.short(vt_symbol, bar.close_price, 1)
                    self.write_log(f"开空: {vt_symbol}, 信号值: {signal_value:.4f}")
            elif current_pos > 0:  # 当前持多
                if signal_value < -self.close_threshold:  # 平多
                    self.sell(vt_symbol, bar.close_price, abs(current_pos))
                    self.write_log(f"平多: {vt_symbol}, 信号值: {signal_value:.4f}")
            elif current_pos < 0:  # 当前持空
                if signal_value > self.close_threshold:  # 平空
                    self.cover(vt_symbol, bar.close_price, abs(current_pos))
                    self.write_log(f"平空: {vt_symbol}, 信号值: {signal_value:.4f}")
    
    def on_trade(self, trade):
        """成交回调"""
        self.write_log(f"成交执行: {trade.vt_symbol}, 方向: {trade.direction.value}, 价格: {trade.price}, 数量: {trade.volume}")


# =============================================================================
# 主要执行函数（增强版）
# =============================================================================

def run_complete_backtest(
    name: str = "au_5m_lgb",
    symbol: str = "au2506",  # DataAPI格式合约代码
    vt_symbol: str = "au888.5M.SHFE",  # VnPy格式合约代码
    exchange: str = "SHFE",
    interval: Interval = Interval.MINUTE,
    start_date: str = "2022-01-01",
    end_date: str = "2024-12-31",
    kline_period: str = "5m",
    lookback_window: int = 20,
    predict_window: int = 10,
    train_ratio: float = 0.7,
    valid_ratio: float = 0.15,
    strategy_type: str = "simple", 
    importance_threshold: float = 0.005,
    strategy_settings: dict = None,
    lab_path: str = "./alpha_lab",
    data_save_dir: str = "./data",
    download_data: bool = True,
    dataapi_username: str = None,
    dataapi_password: str = None,
    adjust_type: str = '0'
):
    """
    运行完整回测系统（包含数据下载）
    
    参数:
        name: 信号名称
        symbol: DataAPI格式合约代码（如 'au2506'）
        vt_symbol: VnPy格式合约代码（如 'au888.5M.SHFE'）
        exchange: 交易所代码
        interval: K线间隔
        start_date: 开始日期
        end_date: 结束日期
        kline_period: DataAPI K线周期（'1m', '5m', '15m', '1h', '1d'）
        lookback_window: 回溯窗口
        predict_window: 预测窗口
        train_ratio: 训练集比例
        valid_ratio: 验证集比例
        strategy_type: 策略类型 ("simple")
        importance_threshold: 特征重要性阈值
        strategy_settings: 策略设置参数
        lab_path: AlphaLab路径
        data_save_dir: 数据保存目录
        download_data: 是否下载新数据
        dataapi_username: DataAPI用户名
        dataapi_password: DataAPI密码
        adjust_type: 复权类型
    """
    
    print("🚀 开始运行完整回测系统...")
    print(f"📊 合约: {symbol} -> {vt_symbol}")
    print(f"📅 期间: {start_date} - {end_date}")
    print(f"⏱️  周期: {kline_period}")
    print(f"🎯 策略: {strategy_type}")
    
    # 步骤1：数据下载（如果需要）
    raw_data = None
    if download_data:
        print("\n" + "="*50)
        print("📥 步骤1: 数据下载")
        print("="*50)
        
        downloader = DataDownloader(
            username=dataapi_username,
            password=dataapi_password
        )
        
        # 下载并保存数据
        raw_data, saved_path = downloader.download_and_save(
            symbol=symbol,
            start_date=start_date,
            end_date=end_date,
            kline_period=kline_period,
            save_dir=data_save_dir,
            adjust_type=adjust_type
        )
        
        if raw_data is None:
            print("❌ 数据下载失败，无法继续")
            return None
        
        print(f"✅ 数据下载完成，保存路径: {saved_path}")
    
    # 步骤2：数据格式转换
    print("\n" + "="*50)
    print("🔄 步骤2: 数据格式转换")
    print("="*50)
    
    if raw_data is None:
        # 尝试从本地文件加载数据
        expected_filename = get_data_filename(symbol, start_date, end_date, kline_period, adjust_type)
        data_path = os.path.join(data_save_dir, expected_filename)
        
        if os.path.exists(data_path):
            print(f"📁 从本地加载数据: {data_path}")
            raw_data = pd.read_csv(data_path)
        else:
            print(f"❌ 未找到数据文件: {data_path}")
            print("请先下载数据或提供正确的数据文件")
            return None
    
    # 转换为VnPy格式
    vnpy_data = convert_dataapi_to_vnpy_format(raw_data, vt_symbol)
    if vnpy_data is None:
        print("❌ 数据格式转换失败")
        return None

    # 保存为parquet供AlphaLab使用
    parquet_dir = os.path.join(lab_path, "minute")
    os.makedirs(parquet_dir, exist_ok=True)
    parquet_path = os.path.join(parquet_dir, f"{vt_symbol}.parquet")
    vnpy_data.write_parquet(parquet_path)
    print(f"✅ 已保存parquet数据到: {parquet_path}")
    
    # 步骤3：初始化AlphaLab
    print("\n" + "="*50)
    print("🏭 步骤3: 初始化AlphaLab")
    print("="*50)

    # 根据品种自动推断合约参数
    base_symbol = ''.join([c for c in symbol if c.isalpha()]).lower()
    # 默认参数
    size = 1
    pricetick = 0.01
    if base_symbol.startswith("au"):
        size = 1000
        pricetick = 0.02
    elif base_symbol.startswith("ag"):
        size = 15
        pricetick = 1
    elif base_symbol.startswith("cu"):
        size = 5
        pricetick = 10
    elif base_symbol.startswith("if"):
        size = 300
        pricetick = 0.2
    elif base_symbol.startswith("al"):
        size = 5
        pricetick = 5
    elif base_symbol.startswith("zn"):
        size = 5
        pricetick = 5
    elif base_symbol.startswith("rb"):
        size = 10
        pricetick = 1
    elif base_symbol.startswith("m"):
        size = 10
        pricetick = 1
    elif base_symbol.startswith("i"):
        size = 100
        pricetick = 0.5
    # 可根据需要继续补充其它品种

    lab = AlphaLab(lab_path)
    lab.add_contract_setting(
        vt_symbol=vt_symbol,
        long_rate=0.00025,   # 开多手续费率
        short_rate=0.00025,  # 开空手续费率
        size=size,           # 合约大小
        pricetick=pricetick  # 最小价格变动
    )
    print(f"✅ AlphaLab初始化完成，合约参数：size={size}, pricetick={pricetick}")
    
    # 步骤4：数据分割
    print("\n" + "="*50)
    print("📊 步骤4: 数据分割")
    print("="*50)
    
    start_date_dt = datetime.strptime(start_date, "%Y-%m-%d")
    end_date_dt = datetime.strptime(end_date, "%Y-%m-%d")
    
    total_days = (end_date_dt - start_date_dt).days
    train_days = int(total_days * train_ratio)
    valid_days = int(total_days * valid_ratio)

    train_end_dt = start_date_dt + timedelta(days=train_days)
    valid_end_dt = train_end_dt + timedelta(days=valid_days)

    train_period = (start_date, train_end_dt.strftime("%Y-%m-%d"))
    valid_period = (train_end_dt.strftime("%Y-%m-%d"), valid_end_dt.strftime("%Y-%m-%d"))
    test_period = (valid_end_dt.strftime("%Y-%m-%d"), end_date)
    
    print(f"📚 训练期: {train_period}")
    print(f"🔍 验证期: {valid_period}")
    print(f"🧪 测试期: {test_period}")
    
    # 步骤5：创建数据集
    print("\n" + "="*50)
    print("🧬 步骤5: 特征工程")
    print("="*50)
    
    dataset = EnhancedFuturesMlDataset(
        df=vnpy_data,
        train_period=train_period,
        valid_period=valid_period,
        test_period=test_period,
        lookback_window=lookback_window,
        predict_window=predict_window
    )
    
    # 添加数据处理器
    dataset.add_processor("learn", advanced_process_missing_values)
    dataset.add_processor("learn", enhance_process_drop_na)
    dataset.add_processor("infer", enhance_process_fill_na)
    dataset.add_processor("infer", process_winsorize)
    dataset.add_processor("infer", process_time_series_norm)
    
    print("📋 准备数据集...")
    dataset.prepare_data()
    print("✅ 特征工程完成")
    
    # 步骤6：模型训练
    print("\n" + "="*50)
    print("🤖 步骤6: 模型训练")
    print("="*50)
    
    print("训练初始模型...")
    model_params = {
        "learning_rate": 0.01,
        "num_leaves": 30,
        "num_boost_round": 300,
        "early_stopping_rounds": 30,
    }
    model = LgbModel(**model_params)
    model.fit(dataset)
    
    print("✅ 初始模型训练完成")
    model.detail()
    
    # 步骤7：特征选择
    print("\n" + "="*50)
    print("🎯 步骤7: 特征选择")
    print("="*50)
    
    new_dataset, feature_importances = feature_selection(
        dataset=dataset, 
        model=model, 
        importance_threshold=importance_threshold,
        method='gain'
    )
    
    # 获取选择后的特征（仅用于日志显示）
    selected_features = list(new_dataset.feature_expressions.keys())
    print(f"✅ 特征选择完成，选择了 {len(selected_features)} 个特征")
    print(f"📋 选择的特征: {selected_features[:10]}..." if len(selected_features) > 10 else f"📋 选择的特征: {selected_features}")
    print("📝 注意：最终特征列表将在模型训练完成后保存")
    
    # 步骤8：优化模型训练
    print("\n" + "="*50)
    print("⚡ 步骤8: 优化模型训练")
    print("="*50)
    
    optimized_model_params = {
        "learning_rate": 0.005,
        "num_leaves": 30,  
        "max_depth": 6,
        "min_child_samples": 30,
        "subsample": 0.6,
        "colsample_bytree": 0.8,
        "reg_alpha": 1,
        "reg_lambda": 1,
        "num_boost_round": 500,
        "early_stopping_rounds": 50,
    }
    
    optimized_model = LgbModel(**optimized_model_params)
    optimized_model.fit(new_dataset)
    
    # 获取模型实际使用的特征名称（确保一致性）
    if hasattr(optimized_model, 'model') and optimized_model.model is not None:
        actual_model_features = optimized_model.model.feature_name()
        print(f"✅ 模型实际使用特征数量: {len(actual_model_features)}")
        print(f"📋 模型实际特征: {actual_model_features[:10]}..." if len(actual_model_features) > 10 else f"📋 模型实际特征: {actual_model_features}")
        
        # 创建目录
        models_dir = "models"
        features_dir = "features"
        os.makedirs(models_dir, exist_ok=True)
        os.makedirs(features_dir, exist_ok=True)
        
        # 保存最终模型文件（实盘使用）
        final_model_filename = f"{symbol}_{start_date}_{end_date}.pkl"
        final_model_path = os.path.join(models_dir, final_model_filename)
        with open(final_model_path, 'wb') as f:
            pickle.dump(optimized_model.model, f)
        print(f"✅ 最终模型已保存到: {final_model_path}")
        
        # 保存最终特征文件（实盘使用）
        final_features_filename = f"selected_features_{symbol}_{start_date}_{end_date}.pkl"
        final_features_path = os.path.join(features_dir, final_features_filename)
        with open(final_features_path, 'wb') as f:
            pickle.dump(actual_model_features, f)
        print(f"✅ 最终特征文件已保存到: {final_features_path}")
        
        # 验证一致性
        if len(actual_model_features) == len(selected_features):
            print("✅ 模型特征数量与选择特征一致")
        else:
            print(f"⚠️ 特征数量差异: 模型={len(actual_model_features)}, 选择={len(selected_features)}")
            print("📝 最终使用模型实际特征作为标准")
        
        print(f"\n🎯 最终输出文件:")
        print(f"   📁 模型文件: {final_model_path}")
        print(f"   📁 特征文件: {final_features_path}")
        print(f"   🔢 特征数量: {len(actual_model_features)}")
    
    print("✅ 优化模型训练完成")
    optimized_model.detail()
    
    # 步骤9：生成预测信号
    print("\n" + "="*50)
    print("📈 步骤9: 生成交易信号")
    print("="*50)
    
    predictions = optimized_model.predict(new_dataset, Segment.TEST)
    
    df_test = new_dataset.fetch_infer(Segment.TEST)
    df_test = df_test.with_columns(pl.Series(predictions).alias("signal"))
    
    signal = df_test["datetime", "vt_symbol", "signal"]
    lab.save_signal(name, signal)
    
    print(f"✅ 交易信号生成完成: {name}")
    
    # 步骤10：运行回测
    print("\n" + "="*50)
    print("📊 步骤10: 策略回测")
    print("="*50)
    
    # 加载信号数据
    signal_data = lab.load_signal(name)
    
    # 创建回测引擎
    engine = BacktestingEngine(lab)
    
    # 设置回测参数
    engine.set_parameters(
        vt_symbols=[vt_symbol],
        interval=interval,
        start=valid_end_dt.strftime("%Y-%m-%d"),
        end=end_date,
        capital=100000
    )
    
    # 设置策略参数
    if strategy_settings is None:
        strategy_settings = {
            "open_threshold": 0.01,
            "close_threshold": 0.01,
            "stop_loss": 0.02,
            "profit_target": 0.04,
            "max_holding_days": 15,
        }
    

    engine.add_strategy(SignalStrategy, strategy_settings, signal_data)
    
    # 运行回测
    print("🔄 开始执行回测...")
    engine.load_data()
    engine.run_backtesting()
    
    # 计算和显示结果
    result = engine.calculate_result()
    # 兼容性判断，避免daily_df不存在时报错
    if hasattr(engine, 'daily_df') and engine.daily_df is not None:
        # 先判断max_drawdown是否为0，防止ZeroDivisionError
        df = engine.daily_df
        if "drawdown" in df.columns and df["drawdown"].min() == 0:
            print('⚠️ 回测最大回撤为0，跳过统计，防止除零错误。')
        else:
            engine.calculate_statistics()
            engine.show_chart()
    else:
        print('⚠️ 回测未生成每日统计数据，无法计算统计指标。')
    
    print("\n" + "="*50)
    print("🎉 回测完成!")
    print("="*50)
    
    # 返回完整结果
    return {
        'raw_data': raw_data,
        'vnpy_data': vnpy_data,
        'dataset': new_dataset,
        'model': optimized_model,
        'signal': signal_data,
        'backtest_result': result,
        'feature_importances': feature_importances,
        'strategy_settings': strategy_settings
    }


# =============================================================================
# 便捷函数
# =============================================================================

def quick_backtest_with_download(
    symbol: str,
    dataapi_username: str,
    dataapi_password: str,
    start_date: str = "2023-01-01",
    end_date: str = "2024-12-31",
    strategy_type: str = "simple"
):
    """
    快速回测（包含数据下载）
    
    参数:
        symbol: 合约代码（如 'au2506'）
        dataapi_username: DataAPI用户名
        dataapi_password: DataAPI密码
        start_date: 开始日期
        end_date: 结束日期
        strategy_type: 策略类型
    """
    
    # 根据合约代码推断交易所
    if symbol.startswith(('au', 'ag', 'cu', 'al', 'zn', 'pb', 'ni', 'sn', 'ss')):
        exchange = "SHFE"
        contract_type = "5M"
    elif symbol.startswith(('a', 'c', 'm', 'y', 'p', 'v', 'i', 'j', 'jm', 'jd', 'fb', 'bb', 'pp', 'l', 'eg', 'pg', 'eb')):
        exchange = "DCE"
        contract_type = "5M"
    elif symbol.startswith(('sr', 'cf', 'ta', 'ma', 'zc', 'fg', 'oi', 'rm', 'rs', 'ri', 'jr', 'lr', 'wh', 'pm', 'sf', 'sm')):
        exchange = "CZCE"
        contract_type = "5M"
    else:
        exchange = "SHFE"  # 默认
        contract_type = "5M"
    
    # 构造VnPy格式合约代码
    base_symbol = ''.join([c for c in symbol if c.isalpha()])  # 提取字母部分
    vt_symbol = f"{base_symbol}888.{contract_type}.{exchange}"
    
    print(f"📊 自动推断：{symbol} -> {vt_symbol}")
    
    return run_complete_backtest(
        name=f"{symbol}_backtest",
        symbol=symbol,
        vt_symbol=vt_symbol,
        start_date=start_date,
        end_date=end_date,
        kline_period=kline_period,
        download_data=False,  # 不再下载
        data_save_dir="./data",
        adjust_type=adjust_type,
        strategy_type=strategy_type,
        dataapi_username=dataapi_username,
        dataapi_password=dataapi_password
    )


# =============================================================================
# 主程序入口
# =============================================================================

if __name__ == "__main__":
    print("=== 期货ML回测系统 ===")
    print("请先选择要回测的品种参数")
    
    # 1. 让用户输入品种参数
    symbol = input("请输入合约代码（如 au888）: ").strip()
    adjust_type = input("是否复权？0=不复权，1=后复权（默认0）: ").strip() or "0"
    kline_period = input("K线周期（如 1m, 5m, 15m, 1h, 1d，默认5m）: ").strip() or "5m"
    start_date = input("开始日期（如 2023-01-01）: ").strip()
    end_date = input("结束日期（如 2024-12-31）: ").strip()
    # 新增：让用户选择回测策略类型

    # 2. 询问是否需要下载数据
    need_download = input("是否需要下载数据？(y/n): ").strip().lower()
    data = None
    path = None
    if need_download == "y":
        username = input("DataAPI用户名: ").strip()
        password = input("DataAPI密码: ").strip()
        downloader = DataDownloader(username=username, password=password)
        data, path = downloader.download_and_save(
            symbol=symbol,
            start_date=start_date,
            end_date=end_date,
            kline_period=kline_period,
            save_dir="./data",
            adjust_type=adjust_type
        )
        if data is not None:
            print(f"✅ 数据下载完成: {len(data)} 条记录")
            print(f"📁 保存路径: {path}")
        else:
            print("❌ 数据下载失败")
            print("程序终止。")
            sys.exit(0)
    else:
        # 不下载数据，直接尝试读取本地数据
        expected_filename = get_data_filename(symbol, start_date, end_date, kline_period, adjust_type)
        path = os.path.join("./data", expected_filename)
        if not os.path.exists(path):
            print(f"❌ 未找到本地数据文件: {path}")
            print("请先下载数据后再回测。")
            sys.exit(0)
        else:
            print(f"📁 使用本地数据文件: {path}")

    # 3. 自动推断vt_symbol
    if symbol.startswith(('au', 'ag', 'cu', 'al', 'zn', 'pb', 'ni', 'sn', 'ss')):
        exchange = "SHFE"
        contract_type = "5M"
    elif symbol.startswith(('a', 'c', 'm', 'y', 'p', 'v', 'i', 'j', 'jm', 'jd', 'fb', 'bb', 'pp', 'l', 'eg', 'pg', 'eb')):
        exchange = "DCE"
        contract_type = "5M"
    elif symbol.startswith(('sr', 'cf', 'ta', 'ma', 'zc', 'fg', 'oi', 'rm', 'rs', 'ri', 'jr', 'lr', 'wh', 'pm', 'sf', 'sm')):
        exchange = "CZCE"
        contract_type = "5M"
    else:
        exchange = "SHFE"
        contract_type = "5M"
    base_symbol = ''.join([c for c in symbol if c.isalpha()])
    vt_symbol = f"{base_symbol}888.{contract_type}.{exchange}"

    # 4. 进入回测主流程，自动读取刚才下载或本地的数据
    run_complete_backtest(
        name=f"{symbol}_backtest",
        symbol=symbol,
        vt_symbol=vt_symbol,
        start_date=start_date,
        end_date=end_date,
        kline_period=kline_period,
        download_data=False,  # 不再下载
        data_save_dir="./data",
        adjust_type=adjust_type,
        # 其它参数用默认值
    )
    print("\n🎉 程序运行完成!")