Traceback (most recent call last):
  File "E:\量化策略\专享策略20_基于LLM的订单流日内交易策略\3.31版本_new\ml_ctp.py", line 2119, in OnRspOrderInsert
    error_msg = pRspInfo.get('ErrorMsg', '').decode('gbk') if pRspInfo.get('ErrorMsg') else ''
AttributeError: 'str' object has no attribute 'decode'. Did you mean: 'encode'?
Exception ignored in: 'AlgoPlus.CTP.TraderApiBase.TraderSpi_OnRspOrderInsert'
Traceback (most recent call last):
  File "E:\量化策略\专享策略20_基于LLM的订单流日内交易策略\3.31版本_new\ml_ctp.py", line 2119, in OnRspOrderInsert
    error_msg = pRspInfo.get('ErrorMsg', '').decode('gbk') if pRspInfo.get('ErrorMsg') else ''
AttributeError: 'str' object has no attribute 'decode'